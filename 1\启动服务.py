#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
督办仪表板系统 - 服务启动脚本
快速启动前端和后端服务
"""

import os
import sys
import time
import platform
import subprocess
import webbrowser
import socket
from pathlib import Path

# 设置控制台编码为UTF-8
if platform.system() == 'Windows':
    try:
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        pass

# 颜色代码
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    RESET = '\033[0m'

    @classmethod
    def disable_on_windows(cls):
        """在Windows上禁用颜色（如果不支持）"""
        if platform.system() == 'Windows':
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
            except:
                for attr in dir(cls):
                    if not attr.startswith('_') and attr != 'disable_on_windows':
                        setattr(cls, attr, '')

Colors.disable_on_windows()

def print_banner():
    """显示启动横幅"""
    print()
    print(f"{Colors.CYAN}========================================{Colors.RESET}")
    print(f"{Colors.CYAN}    督办仪表板系统 - 服务启动    {Colors.RESET}")
    print(f"{Colors.CYAN}========================================{Colors.RESET}")
    print()

def check_environment():
    """检查环境是否已初始化"""
    # 检查项目根目录的虚拟环境
    project_root = Path(__file__).parent.parent.absolute()
    venv_path = project_root / 'venv'

    if not venv_path.exists():
        print(f"{Colors.RED}❌ 虚拟环境不存在{Colors.RESET}")
        print(f"{Colors.YELLOW}   期望路径: {venv_path}{Colors.RESET}")
        print(f"{Colors.YELLOW}   请先运行初始化脚本: python 初始化环境.py{Colors.RESET}")
        return False

    # 检查虚拟环境中的Python
    system = platform.system()
    if system == 'Windows':
        venv_python = venv_path / 'Scripts' / 'python.exe'
    else:
        venv_python = venv_path / 'bin' / 'python'

    if not venv_python.exists():
        print(f"{Colors.RED}❌ 虚拟环境Python不存在{Colors.RESET}")
        print(f"{Colors.YELLOW}   期望路径: {venv_python}{Colors.RESET}")
        print(f"{Colors.YELLOW}   请先运行初始化脚本: python 初始化环境.py{Colors.RESET}")
        return False

    print(f"{Colors.GREEN}✅ 环境检查通过{Colors.RESET}")
    print(f"{Colors.BLUE}   虚拟环境路径: {venv_path}{Colors.RESET}")
    return True

def get_venv_python():
    """获取虚拟环境中的Python路径"""
    # 获取项目根目录的虚拟环境
    project_root = Path(__file__).parent.parent.absolute()
    system = platform.system()
    if system == 'Windows':
        return project_root / 'venv' / 'Scripts' / 'python.exe'
    else:
        return project_root / 'venv' / 'bin' / 'python'

def check_port(port):
    """检查端口是否被占用"""
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        result = sock.connect_ex(('localhost', port))
        return result == 0
    finally:
        sock.close()

def test_port_binding(port):
    """测试端口绑定权限"""
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(('localhost', port))
        sock.close()
        return True, None
    except PermissionError as e:
        return False, f"权限错误: {e}"
    except OSError as e:
        if "access" in str(e).lower() or "permission" in str(e).lower():
            return False, f"访问权限错误: {e}"
        return False, f"端口绑定错误: {e}"
    except Exception as e:
        return False, f"未知错误: {e}"

def check_admin_privileges():
    """检查是否以管理员权限运行"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def suggest_port_solutions(port, error_msg):
    """提供端口问题解决方案"""
    print(f"{Colors.RED}❌ 端口 {port} 绑定失败: {error_msg}{Colors.RESET}")
    print()
    print(f"{Colors.YELLOW}🔧 解决方案:{Colors.RESET}")

    if "权限" in error_msg or "access" in error_msg.lower() or "permission" in error_msg.lower():
        print(f"{Colors.BLUE}   1. 以管理员权限运行:{Colors.RESET}")
        print(f"{Colors.BLUE}      - 右键点击命令提示符/PowerShell{Colors.RESET}")
        print(f"{Colors.BLUE}      - 选择\"以管理员身份运行\"{Colors.RESET}")
        print(f"{Colors.BLUE}      - 重新运行启动脚本{Colors.RESET}")
        print()

    print(f"{Colors.BLUE}   2. 更换端口:{Colors.RESET}")
    print(f"{Colors.BLUE}      - 编辑 .env 文件{Colors.RESET}")
    print(f"{Colors.BLUE}      - 将 FLASK_PORT 改为其他端口 (如 5001, 8000, 8080){Colors.RESET}")
    print()

    print(f"{Colors.BLUE}   3. 检查防火墙设置:{Colors.RESET}")
    print(f"{Colors.BLUE}      - Windows防火墙可能阻止了端口访问{Colors.RESET}")
    print(f"{Colors.BLUE}      - 添加端口 {port} 到防火墙例外{Colors.RESET}")
    print()

    print(f"{Colors.BLUE}   4. 检查杀毒软件:{Colors.RESET}")
    print(f"{Colors.BLUE}      - 某些杀毒软件可能阻止套接字绑定{Colors.RESET}")
    print(f"{Colors.BLUE}      - 临时禁用实时保护或添加例外{Colors.RESET}")
    print()

    if not check_admin_privileges():
        print(f"{Colors.YELLOW}   ⚠️  当前未以管理员权限运行，建议尝试方案1{Colors.RESET}")
    else:
        print(f"{Colors.GREEN}   ✅ 当前以管理员权限运行{Colors.RESET}")
    print()

def kill_process_on_port(port):
    """终止占用指定端口的进程"""
    system = platform.system()
    try:
        if system == 'Windows':
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        subprocess.run(['taskkill', '/PID', pid, '/F'], capture_output=True)
                        return True
        else:
            result = subprocess.run(['lsof', '-Pi', f':{port}', '-sTCP:LISTEN', '-t'],
                                  capture_output=True, text=True)
            if result.stdout.strip():
                pid = result.stdout.strip()
                subprocess.run(['kill', '-9', pid], capture_output=True)
                return True
    except:
        pass
    return False

def open_browser(file_path):
    """在默认浏览器中打开文件"""
    try:
        file_url = f"file://{file_path.as_posix()}"
        webbrowser.open(file_url)
        return True
    except Exception as e:
        print(f"{Colors.YELLOW}⚠️  无法自动打开浏览器: {e}{Colors.RESET}")
        print(f"{Colors.BLUE}请手动访问: file://{file_path}{Colors.RESET}")
        return False

def print_success_info(index_file, port):
    """显示启动成功信息"""
    print()
    print(f"{Colors.GREEN}========================================{Colors.RESET}")
    print(f"{Colors.GREEN}    🎉 督办仪表板系统启动成功！    {Colors.RESET}")
    print(f"{Colors.GREEN}========================================{Colors.RESET}")
    print()
    print(f"{Colors.CYAN}📍 访问地址:{Colors.RESET}")
    print(f"{Colors.BLUE}   🌐 前端界面: file://{index_file.as_posix()}{Colors.RESET}")
    print(f"{Colors.BLUE}   🔧 后端API:  http://localhost:{port}{Colors.RESET}")
    print()
    print(f"{Colors.CYAN}📖 功能说明:{Colors.RESET}")
    print(f"{Colors.BLUE}   • 数据管理: 支持CSV、Excel文件导入{Colors.RESET}")
    print(f"{Colors.BLUE}   • 短信提醒: 集成阿里云短信服务{Colors.RESET}")
    print(f"{Colors.BLUE}   • 统计分析: 多维度数据统计{Colors.RESET}")
    print(f"{Colors.BLUE}   • 智能过滤: 支持多种过滤条件{Colors.RESET}")
    print()
    print(f"{Colors.CYAN}🔧 配置文件:{Colors.RESET}")
    print(f"{Colors.BLUE}   • 环境配置: .env{Colors.RESET}")
    print(f"{Colors.BLUE}   • 系统设置: 点击右上角\"⚙️ 系统设置\"{Colors.RESET}")
    print()
    print(f"{Colors.YELLOW}⚠️  注意事项:{Colors.RESET}")
    print(f"{Colors.BLUE}   • 请保持此窗口打开以维持后端服务{Colors.RESET}")
    print(f"{Colors.BLUE}   • 按 Ctrl+C 可停止后端服务{Colors.RESET}")
    print(f"{Colors.BLUE}   • 如需配置短信服务，请编辑 .env 文件{Colors.RESET}")
    print()
    print(f"{Colors.GREEN}========================================{Colors.RESET}")
    print()

def main():
    """主函数"""
    try:
        # 显示启动横幅
        print_banner()

        # 获取项目目录
        project_dir = Path(__file__).parent.absolute()
        os.chdir(project_dir)

        # 检查环境
        print(f"{Colors.BLUE}🔍 检查环境...{Colors.RESET}")
        if not check_environment():
            return 1

        # 打开浏览器
        print(f"{Colors.BLUE}🌐 打开督办仪表板主页面...{Colors.RESET}")
        index_file = project_dir / 'front' / 'index.html'

        if not index_file.exists():
            print(f"{Colors.RED}❌ 错误: 未找到 index.html 文件{Colors.RESET}")
            print(f"{Colors.YELLOW}   期望路径: {index_file}{Colors.RESET}")
            return 1

        open_browser(index_file)
        print(f"{Colors.GREEN}✅ 浏览器已打开督办仪表板{Colors.RESET}")

        # 等待2秒让浏览器启动
        time.sleep(2)

        # 从环境变量读取端口配置
        from dotenv import load_dotenv
        # 加载 backend 目录下的 .env 文件
        env_file = project_dir / 'backend' / '.env'
        if env_file.exists():
            load_dotenv(env_file)
            print(f"{Colors.GREEN}✅ 已加载配置文件: {env_file}{Colors.RESET}")
        else:
            print(f"{Colors.YELLOW}⚠️  未找到配置文件: {env_file}{Colors.RESET}")
            print(f"{Colors.BLUE}   将使用默认配置{Colors.RESET}")
        port = int(os.environ.get('FLASK_PORT', 5000))

        # 检查端口
        print(f"{Colors.BLUE}🔍 检查端口{port}...{Colors.RESET}")
        if check_port(port):
            print(f"{Colors.YELLOW}⚠️  端口{port}已被占用{Colors.RESET}")
            print(f"{Colors.BLUE}正在尝试终止占用进程...{Colors.RESET}")

            if kill_process_on_port(port):
                time.sleep(2)
                if check_port(port):
                    print(f"{Colors.RED}❌ 无法释放端口{port}，请手动处理{Colors.RESET}")
                    return 1
                print(f"{Colors.GREEN}✅ 端口{port}已释放{Colors.RESET}")
            else:
                print(f"{Colors.RED}❌ 无法释放端口{port}，请手动处理{Colors.RESET}")
                return 1
        else:
            print(f"{Colors.GREEN}✅ 端口{port}可用{Colors.RESET}")

        # 测试端口绑定权限
        print(f"{Colors.BLUE}🔍 测试端口绑定权限...{Colors.RESET}")
        can_bind, bind_error = test_port_binding(port)
        if not can_bind:
            suggest_port_solutions(port, bind_error)
            return 1
        else:
            print(f"{Colors.GREEN}✅ 端口绑定权限正常{Colors.RESET}")

        # 显示启动成功信息
        print_success_info(index_file, port)

        # 启动后端服务
        print(f"{Colors.BLUE}🚀 正在启动后端服务...{Colors.RESET}")
        print(f"{Colors.BLUE}如果看到 \"Running on http://127.0.0.1:{port}\" 表示启动成功{Colors.RESET}")
        print()

        # 使用虚拟环境中的Python运行服务
        venv_python = get_venv_python()
        backend_script = project_dir / 'backend' / 'backend_api.py'

        if not backend_script.exists():
            print(f"{Colors.RED}❌ 错误: 未找到后端脚本 {backend_script}{Colors.RESET}")
            return 1

        # 切换到 backend 目录运行服务（确保相对路径正确）
        backend_dir = project_dir / 'backend'
        try:
            subprocess.run([str(venv_python), str(backend_script)], cwd=str(backend_dir))
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 后端服务已停止{Colors.RESET}")
            print(f"{Colors.BLUE}浏览器页面仍可正常使用（无后端功能）{Colors.RESET}")

        return 0

    except Exception as e:
        print(f"{Colors.RED}❌ 启动失败: {e}{Colors.RESET}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
