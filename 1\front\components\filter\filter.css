/* 过滤器样式 */
.filter-tags {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.filter-tag {
    padding: 8px 15px;
    background: #e9ecef;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-tag.active {
    background: #667eea;
    color: white;
}

.filter-tag:hover {
    border-color: #667eea;
}

.filter-count {
    background: rgba(255, 255, 255, 0.3);
    color: #333;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    min-width: 20px;
    text-align: center;
}

.filter-tag.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 快到期标签特殊样式 - 仅在有数据时显示 */
.filter-tag[data-filter="urgent"].has-data {
    background: #ffebee;
    border-color: #e74c3c;
    color: #c62828;
    font-weight: bold;
}

.filter-tag[data-filter="urgent"].has-data:hover {
    background: #ffcdd2;
    border-color: #d32f2f;
}

.filter-tag[data-filter="urgent"].has-data.active {
    background: #e74c3c;
    color: white;
    border-color: #c62828;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.filter-tag[data-filter="urgent"].has-data .filter-count {
    background: rgba(231, 76, 60, 0.1);
    color: #c62828;
    border: 1px solid #e74c3c;
}

.filter-tag[data-filter="urgent"].has-data.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 已过期标签特殊样式 - 仅在有数据时显示 */
.filter-tag[data-filter="overdue"].has-data {
    background: #ffcdd2;
    border-color: #d32f2f;
    color: #b71c1c;
    font-weight: bold;
    animation: overdueBlink 2s infinite;
}

.filter-tag[data-filter="overdue"].has-data:hover {
    background: #ef9a9a;
    border-color: #b71c1c;
}

.filter-tag[data-filter="overdue"].has-data.active {
    background: #d32f2f;
    color: white;
    border-color: #b71c1c;
    box-shadow: 0 2px 8px rgba(211, 47, 47, 0.4);
}

.filter-tag[data-filter="overdue"].has-data .filter-count {
    background: rgba(211, 47, 47, 0.1);
    color: #b71c1c;
    border: 1px solid #d32f2f;
}

.filter-tag[data-filter="overdue"].has-data.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

@keyframes overdueBlink {
    0% { border-color: #d32f2f; }
    50% { border-color: #b71c1c; }
    100% { border-color: #d32f2f; }
}

/* 未完成任务标签 - 仅在有数据时显示特殊样式 */
.filter-tag[data-filter="incomplete"].has-data {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
    font-weight: bold;
}

.filter-tag[data-filter="incomplete"].has-data:hover {
    background: #ffeaa7;
    border-color: #e0a800;
}

.filter-tag[data-filter="incomplete"].has-data.active {
    background: #ffc107;
    color: #212529;
    border-color: #e0a800;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.filter-tag[data-filter="incomplete"].has-data .filter-count {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid #ffc107;
}

.filter-tag[data-filter="incomplete"].has-data.active .filter-count {
    background: rgba(33, 37, 41, 0.1);
    color: #212529;
    border: 1px solid rgba(33, 37, 41, 0.2);
}

/* 已完成任务标签 - 仅在有数据时显示特殊样式 */
.filter-tag[data-filter="completed"].has-data {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
    font-weight: bold;
}

.filter-tag[data-filter="completed"].has-data:hover {
    background: #c3e6cb;
    border-color: #1e7e34;
}

.filter-tag[data-filter="completed"].has-data.active {
    background: #28a745;
    color: white;
    border-color: #1e7e34;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.filter-tag[data-filter="completed"].has-data .filter-count {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border: 1px solid #28a745;
}

.filter-tag[data-filter="completed"].has-data.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-tags {
        justify-content: center;
    }

    .filter-tag {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
}
