import requests
import hashlib
import urllib.parse
import xml.etree.ElementTree as ET

class SmsApiClient:
    """
    短信接口客户端，用于发送短信和查询短信状态报告。
    """
    BASE_URL = "http://sms.ceic.com:8011/http/"

    def __init__(self, account, password):
        """
        初始化短信客户端。

        Args:
            account (str): 您的平台账号。
            password (str): 您的平台密码（将进行 MD5 加密）。
        """
        self.account = account
        self.password = self._md5_encrypt(password) # 密码进行MD5加密

    def _md5_encrypt(self, text):
        """
        对文本进行 MD5 加密。
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _build_post_data(self, payload):
        """
        构建 application/x-www-form-urlencoded 格式的 POST 请求体。
        """
        # 手动构建数据字符串，确保已预先处理的参数（如Content）不会被requests再次编码
        encoded_params = []
        for key, value in payload.items():
            # urllib.parse.quote 对非 ASCII 字符进行编码，这里主要确保 Content 之外的参数也正确编码
            # 但由于 Content 已经通过 urllib.parse.quote(content.encode('gbk')) 处理，
            # 这里只需直接使用其值，其他参数默认以UTF-8编码并URL编码。
            # 为了兼容性，这里统一使用 str() 确保所有值都是字符串，然后进行 URL 编码
            if key == "Content":
                # Content 已经预编码，直接使用
                encoded_params.append(f"{key}={value}")
            else:
                encoded_params.append(f"{key}={urllib.parse.quote(str(value))}")
        return "&".join(encoded_params)

    def send_sms(self, phone, content, send_time=None):
        """
        发送单条或多条内容相同的短信。

        Args:
            phone (str): 手机号码，多个号码用英文逗号分隔，最多100个。
            content (str): 短信内容。
            send_time (str, optional): 定时发送时间，格式为 MM-dd-yyyy HH:mm:ss。
                                       为空表示立即发送。

        Returns:
            str: API 响应的 XML 字符串，或错误信息。
        """
        url = self.BASE_URL + "SendSms"

        # 根据接口规范，Content 需要进行 GBK 编码后，再进行 URL 编码
        encoded_content = urllib.parse.quote(content.encode('gbk'))

        payload = {
            "Account": self.account,
            "Password": self.password,
            "Phone": phone,
            "Content": encoded_content # 使用预编码的Content
        }
        if send_time:
            payload["SendTime"] = send_time

        data_str = self._build_post_data(payload)

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=GBK'
        }

        print(f"正在发送短信到 {phone}...")
        try:
            # requests.post 的 data 参数如果是字节串，则不会再次编码
            response = requests.post(url, data=data_str.encode('ascii'), headers=headers)
            response.raise_for_status()  # 如果响应状态码是 4xx 或 5xx，则抛出 HTTPError
            return response.text
        except requests.exceptions.RequestException as e:
            return f"发送短信失败: {e}"

    def get_report(self, sms_id):
        """
        通过批次号获取短信状态报告。

        Args:
            sms_id (str): 批次号，对应下行接口返回的 smsID。

        Returns:
            str: API 响应的 XML 字符串，或错误信息。
        """
        url = self.BASE_URL + "GetReport"
        payload = {
            "Account": self.account,
            "Password": self.password,
            "SmsId": sms_id
        }

        data_str = self._build_post_data(payload)

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=GBK'
        }

        print(f"正在查询短信状态报告 {sms_id}...")
        try:
            response = requests.post(url, data=data_str.encode('ascii'), headers=headers)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            return f"获取状态报告失败: {e}"

if __name__ == "__main__":
    # TODO: 请替换为您的实际账号和密码
    # 注意：这里的密码是明文，SmsApiClient 会自动进行 MD5 加密
    YOUR_ACCOUNT = "GXGSRWDB" # 示例账号
    YOUR_PASSWORD = "wYjp3!2Cbj" # 示例密码

    client = SmsApiClient(YOUR_ACCOUNT, YOUR_PASSWORD)

    # --- 示例：发送短信 ---
    # 请替换为实际的手机号码和内容
    target_phone_number = "***********" # 示例手机号
    test_message_content = "这是一条测试短信，请勿回复。"
    # 您也可以指定定时发送时间，例如：
    # scheduled_send_time = "06-04-2024 16:55:01"

    send_response = client.send_sms(target_phone_number, test_message_content) # , send_time=scheduled_send_time
    print("短信发送响应:")
    print(send_response)

    # 解析 XML 响应以获取 smsID
    try:
        root = ET.fromstring(send_response)
        response_code = root.find("response").text
        print(f"响应码: {response_code}")
        if response_code == "1": # 假设 1 代表成功
            sms_id_sent = root.find(".//smsID").text
            print(f"成功发送短信，获取到 smsID: {sms_id_sent}")

            # --- 示例：查询短信状态报告 ---
            # 使用上面获取到的 smsID 进行查询
            # print(f"\n尝试查询 smsID {sms_id_sent} 的状态报告...")
            # report_response = client.get_report(sms_id_sent)
            # print("状态报告响应:")
            # print(report_response)
        else:
            print("短信发送失败，无法获取 smsID 进行查询。")
    except ET.ParseError as e:
        print(f"解析发送响应 XML 失败: {e}")
    except AttributeError:
        print("发送响应中未找到预期的 XML 元素 (response 或 smsID)。")
    except Exception as e:
        print(f"处理发送响应时发生错误: {e}")

    # 请注意：
    # 1. 运行此代码需要安装 `requests` 库：`pip install requests`
    # 2. 将 `