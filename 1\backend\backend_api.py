from flask import Flask, request, jsonify
from sms_api import SmsApiClient
from flask_cors import CORS
import xml.etree.ElementTree as ET
import os
from dotenv import load_dotenv

app = Flask(__name__)
CORS(app) # 允许跨域请求

@app.route('/api/sms/send', methods=['POST'])
def send_sms():
    data = request.get_json()
    phone_number = data.get('phoneNumber')
    content = data.get('content')
    account = data.get('account')
    password = data.get('password')

    if not all([phone_number, content, account, password]):
        return jsonify({'success': False, 'message': '缺少必要的参数'}), 400

    client = SmsApiClient(account, password)
    response_xml = client.send_sms(phone_number, content)

    try:
        root = ET.fromstring(response_xml)
        response_code = root.find("response").text
        # 假设 1 代表成功，其他为失败
        if response_code == "1":
            sms_id = root.find(".//smsID").text
            return jsonify({
                'success': True,
                'message': '短信发送成功',
                'data': {'smsId': sms_id, 'responseCode': response_code}
            }), 200
        else:
            error_message = root.find("message").text if root.find("message") is not None else "未知错误"
            return jsonify({
                'success': False,
                'message': f'短信发送失败: {error_message}',
                'data': {'responseCode': response_code}
            }), 200 # 返回 200 但 success: False 表示业务失败
    except ET.ParseError as e:
        print(f"解析短信API响应XML失败: {e}")
        return jsonify({'success': False, 'message': '解析短信API响应失败'}), 500
    except AttributeError:
        print("短信API响应XML中未找到预期的元素 (response 或 smsID)。")
        return jsonify({'success': False, 'message': '短信API响应格式不正确'}), 500
    except Exception as e:
        print(f"处理短信API响应时发生错误: {e}")
        return jsonify({'success': False, 'message': f'服务器内部错误: {e}'}), 500

if __name__ == '__main__':
    # 在生产环境中，请使用 Gunicorn 或其他 WSGI 服务器
    # 例如：gunicorn -w 4 -b 0.0.0.0:5000 backend_api:app
    load_dotenv()
    port = int(os.environ.get('FLASK_PORT', 5000))
    print(f"Backend API Server running on http://localhost:{port}")
    app.run(debug=True, port=port) 