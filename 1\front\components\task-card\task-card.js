/**
 * 任务卡片组件
 */

class TaskCard {
    constructor() {
        this.container = document.getElementById('tasksContainer');
    }

    /**
     * 渲染任务列表
     * @param {Array} tasks - 任务数组
     */
    renderTasks(tasks) {
        if (!this.container) return;

        this.container.innerHTML = '';

        if (tasks.length === 0) {
            this.container.innerHTML = '<div class="loading">暂无数据</div>';
            return;
        }

        tasks.forEach((task, index) => {
            const taskCard = this.createTaskCard(task, index);
            this.container.appendChild(taskCard);
        });
    }

    /**
     * 创建单个任务卡片
     * @param {Object} task - 任务对象
     * @param {number} index - 任务索引
     * @returns {HTMLElement} 任务卡片元素
     */
    createTaskCard(task, index) {
        const card = document.createElement('div');
        card.className = 'task-card';

        const status = task['整改措施完成情况'] || '';
        const isCompleted = status.includes('已完成');
        const urgencyLevel = this.getUrgencyLevel(task);
        const urgent = this.isUrgent(task);

        if (isCompleted) {
            card.classList.add('completed');
        } else if (urgent) {
            card.classList.add('urgent');
            // 根据紧急程度添加额外的样式类
            if (urgencyLevel === 'critical') {
                card.classList.add('critical');
            } else if (urgencyLevel === 'overdue') {
                card.classList.add('overdue');
            }
        }

        // 确定状态样式和文本
        let statusClass, statusText;
        if (isCompleted) {
            statusClass = 'status-completed';
            statusText = '已完成';
        } else if (urgencyLevel === 'overdue') {
            statusClass = 'status-overdue';
            statusText = '⚠️ 已过期';
        } else if (urgencyLevel === 'critical') {
            statusClass = 'status-critical';
            statusText = '🔥 紧急';
        } else if (urgent) {
            statusClass = 'status-urgent';
            statusText = '🚨 快到期';
        } else {
            statusClass = 'status-incomplete';
            statusText = '进行中';
        }

        card.innerHTML = `
            <div class="task-content">
                <div class="task-header">
                    <div class="task-title">${task['问题概述'] || '未提供'}</div>
                    <div class="task-status ${statusClass}">${statusText}</div>
                </div>
                <div class="task-details">
                    <div class="task-detail">
                        <strong>具体问题:</strong>
                        <span>${task['具体问题'] || '未提供'}</span>
                    </div>
                    <div class="task-detail">
                        <strong>整改措施:</strong>
                        <span>${task['整改措施'] || '未提供'}</span>
                    </div>
                    <div class="task-detail">
                        <strong>整改类型:</strong>
                        <span>${task['整改类型'] || '未提供'}</span>
                    </div>
                    <div class="task-detail">
                        <strong>计划时间:</strong>
                        <span>${task['计划完成时间'] || '未提供'}</span>
                    </div>
                    <div class="task-detail">
                        <strong>责任领导:</strong>
                        <span>${task['责任领导'] || '未提供'}</span>
                    </div>
                    <div class="task-detail">
                        <strong>牵头部门:</strong>
                        <span>${task['牵头整改部门'] || '未提供'}</span>
                    </div>
                    <div class="task-detail">
                        <strong>完成情况:</strong>
                        <span>${task['整改措施完成情况'] || '未提供'}</span>
                    </div>
                </div>
            </div>
            <div class="task-actions">
                ${!isCompleted ? `<button class="reminder-btn" onclick="taskActions.sendReminder(${index})">
                    一键提醒督办
                </button>` : ''}
                <button class="detail-btn"
                        onclick="taskActions.showTaskDetail(${index})"
                        oncontextmenu="event.preventDefault(); taskActions.showTaskDetail(${index}, true);"
                        title="左键：弹窗显示详情 | 右键：新页面显示详情">
                    详情
                </button>
            </div>
        `;

        return card;
    }

    /**
     * 判断任务是否紧急
     * @param {Object} task - 任务对象
     * @returns {boolean} 是否紧急
     */
    isUrgent(task) {
        if (window.configManager) {
            return window.configManager.isTaskUrgent(task);
        }

        // 回退到默认逻辑
        const planTime = task['计划完成时间'] || '';
        const status = task['整改措施完成情况'] || '';

        if (status.includes('已完成')) return false;

        // 检查备注中是否标记为快到期
        if ((task['备注'] || '').includes('快到期')) return true;

        if (planTime && planTime !== '进行中' && planTime !== '长期') {
            try {
                // 支持多种日期格式
                let planDate;
                if (planTime.includes('.')) {
                    planDate = new Date(planTime.replace(/\./g, '-'));
                } else if (planTime.includes('/')) {
                    planDate = new Date(planTime.replace(/\//g, '-'));
                } else {
                    planDate = new Date(planTime);
                }

                const today = new Date();
                today.setHours(0, 0, 0, 0);
                planDate.setHours(0, 0, 0, 0);

                const diffDays = Math.ceil((planDate - today) / (1000 * 60 * 60 * 24));

                // 15天内到期且未过期的任务标记为快到期
                return diffDays <= 15 && diffDays >= 0;
            } catch (e) {
                console.warn('日期解析失败:', planTime, e);
                return false;
            }
        }

        return false;
    }

    /**
     * 获取任务的紧急程度
     * @param {Object} task - 任务对象
     * @returns {string} 紧急程度 ('critical', 'urgent', 'normal')
     */
    getUrgencyLevel(task) {
        if (window.configManager) {
            return window.configManager.getTaskUrgencyLevel(task);
        }

        // 回退到默认逻辑
        const planTime = task['计划完成时间'] || '';
        const status = task['整改措施完成情况'] || '';

        if (status.includes('已完成')) return 'normal';

        if (planTime && planTime !== '进行中' && planTime !== '长期') {
            try {
                let planDate;
                if (planTime.includes('.')) {
                    planDate = new Date(planTime.replace(/\./g, '-'));
                } else if (planTime.includes('/')) {
                    planDate = new Date(planTime.replace(/\//g, '-'));
                } else {
                    planDate = new Date(planTime);
                }

                const today = new Date();
                today.setHours(0, 0, 0, 0);
                planDate.setHours(0, 0, 0, 0);

                const diffDays = Math.ceil((planDate - today) / (1000 * 60 * 60 * 24));

                if (diffDays < 0) return 'overdue'; // 已过期
                if (diffDays <= 3) return 'critical'; // 3天内
                if (diffDays <= 7) return 'warning'; // 7天内
                if (diffDays <= 15) return 'urgent'; // 15天内

                return 'normal';
            } catch (e) {
                return 'normal';
            }
        }

        return 'normal';
    }
}

// 导出任务卡片实例
const taskCard = new TaskCard();
