/**
 * 配置管理器
 * 管理系统的各种配置参数，并确保AppConfig与本地存储同步
 */

class ConfigManager {
    constructor() {
        // 在实例化时加载并应用本地存储的配置到 AppConfig
        this.loadConfigToAppConfig();
        this.listeners = [];
        console.log('ConfigManager: 构造函数中 AppConfig.sms.ceic:', window.AppConfig.sms.ceic);
    }

    /**
     * 将本地存储的配置加载到 window.AppConfig
     */
    loadConfigToAppConfig() {
        try {
            const savedConfig = localStorage.getItem('dashboardSettings'); // 使用与settings.js相同的键
            if (savedConfig) {
                const parsed = JSON.parse(savedConfig);
                // 深度合并 AppConfig 和 parsed，确保子对象也被合并
                this._deepMerge(window.AppConfig, parsed);
                console.log('ConfigManager: 从本地存储加载配置成功并合并到AppConfig');
                console.log('ConfigManager: loadConfigToAppConfig 后 AppConfig.sms.ceic:', window.AppConfig.sms.ceic);
            } else {
                console.log('ConfigManager: 未找到本地存储配置，使用默认AppConfig');
            }
        } catch (e) {
            console.warn('ConfigManager: 加载本地存储配置失败，使用默认AppConfig:', e);
        }
    }

    /**
     * 递归深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     */
    _deepMerge(target, source) {
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key]) && typeof target[key] === 'object' && target[key] !== null && !Array.isArray(target[key])) {
                    // 如果都是对象，则递归合并
                    if (!target[key]) target[key] = {}; // 确保目标对象有该属性
                    this._deepMerge(target[key], source[key]);
                } else {
                    // 否则直接赋值
                    target[key] = source[key];
                }
            }
        }
    }

    /**
     * 保存配置 (直接保存 window.AppConfig 的当前状态)
     */
    saveConfig() {
        try {
            localStorage.setItem('dashboardSettings', JSON.stringify(window.AppConfig));
            this.notifyListeners('configSaved', window.AppConfig);
            console.log('ConfigManager: AppConfig已保存到本地存储');
            return true;
        } catch (e) {
            console.error('ConfigManager: 保存AppConfig失败:', e);
            return false;
        }
    }

    /**
     * 获取配置值
     */
    get(path, defaultValue = undefined) {
        const keys = path.split('.');
        let current = window.AppConfig;
        for (let i = 0; i < keys.length; i++) {
            if (current === undefined || current === null) {
                return defaultValue;
            }
            current = current[keys[i]];
        }
        return current === undefined ? defaultValue : current;
    }

    /**
     * 设置配置值
     */
    set(path, value) {
        const keys = path.split('.');
        let current = window.AppConfig;
        for (let i = 0; i < keys.length - 1; i++) {
            if (current[keys[i]] === undefined || current[keys[i]] === null) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        current[keys[keys.length - 1]] = value;

        this.notifyListeners('configChanged', { path, value });
        
        if (window.AppConfig.features?.enableAutoSave) {
            this.saveConfig();
        }
    }

    /**
     * 批量设置配置 (直接操作 window.AppConfig)
     */
    setMultiple(configObj) {
        // 使用深度合并来更新AppConfig
        this._deepMerge(window.AppConfig, configObj);
        this.notifyListeners('configChanged', configObj);
        
        if (window.AppConfig.features?.enableAutoSave) {
            this.saveConfig();
        }
    }

    /**
     * 重置配置 (从原始AppConfig恢复)
     */
    reset() {
        // 为了重置，我们需要一个原始的AppConfig副本
        // 这里假设AppConfig的初始状态是默认值
        // 重新加载页面会从AppConfig.js文件恢复默认值
        localStorage.removeItem('dashboardSettings');
        this.loadConfigToAppConfig(); // 重新加载以恢复默认值
        this.notifyListeners('configReset', window.AppConfig);
        console.log('ConfigManager: 配置已重置');
    }

    /**
     * 导出配置 (导出 window.AppConfig 的当前状态)
     */
    export() {
        return JSON.stringify(window.AppConfig, null, 2);
    }

    /**
     * 导入配置 (直接导入到 window.AppConfig)
     */
    import(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            // 深度合并导入的配置到AppConfig
            this._deepMerge(window.AppConfig, importedConfig);
            this.saveConfig();
            this.notifyListeners('configImported', window.AppConfig);
            console.log('ConfigManager: 配置导入成功');
            return true;
        } catch (e) {
            console.error('ConfigManager: 导入配置失败:', e);
            return false;
        }
    }

    /**
     * 获取紧急程度阈值配置
     */
    getUrgencyThresholds() {
        return {
            urgent: this.get('time.urgentThreshold'),
            critical: this.get('time.criticalThreshold'),
            warning: this.get('time.warningThreshold')
        };
    }

    /**
     * 判断任务紧急程度
     */
    getTaskUrgencyLevel(task) {
        const planTime = this.get('data.dateFormat') ? task['计划完成时间'] || '' : task['计划完成时间'] || '';
        const status = task['整改措施完成情况'] || '';

        if (status.includes('已完成')) return 'normal';

        // 检查备注中是否标记为快到期
        if ((task['备注'] || '').includes('快到期')) return 'urgent';

        if (planTime && planTime !== '进行中' && planTime !== '长期') {
            try {
                // 支持多种日期格式
                let planDate;
                if (planTime.includes('.')) {
                    planDate = new Date(planTime.replace(/\./g, '-'));
                } else if (planTime.includes('/')) {
                    planDate = new Date(planTime.replace(/\//g, '-'));
                } else {
                    planDate = new Date(planTime);
                }

                const today = new Date();
                today.setHours(0, 0, 0, 0);
                planDate.setHours(0, 0, 0, 0);

                const diffDays = Math.ceil((planDate - today) / (1000 * 60 * 60 * 24));
                
                const thresholds = this.getUrgencyThresholds();
                
                if (diffDays < 0) return 'overdue'; // 已过期
                if (diffDays <= thresholds.critical) return 'critical'; // 紧急
                if (diffDays <= thresholds.warning) return 'warning'; // 警告
                if (diffDays <= thresholds.urgent) return 'urgent'; // 快到期
                
                return 'normal';
            } catch (e) {
                console.warn('日期解析失败:', planTime, e);
                return 'normal';
            }
        }

        return 'normal';
    }

    /**
     * 判断任务是否紧急（快到期或更紧急）
     */
    isTaskUrgent(task) {
        const level = this.getTaskUrgencyLevel(task);
        return ['urgent', 'warning', 'critical', 'overdue'].includes(level);
    }

    /**
     * 判断任务是否已过期
     */
    isTaskOverdue(task) {
        return this.getTaskUrgencyLevel(task) === 'overdue';
    }

    /**
     * 添加配置变化监听器
     */
    addListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * 移除配置变化监听器
     */
    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (e) {
                console.error('配置监听器执行失败:', e);
            }
        });
    }

    /**
     * 验证配置有效性
     */
    validateConfig() {
        const errors = [];
        
        // 验证阈值设置
        const thresholds = this.getUrgencyThresholds();
        if (thresholds.critical >= thresholds.warning) {
            errors.push('紧急阈值应小于警告阈值');
        }
        if (thresholds.warning >= thresholds.urgent) {
            errors.push('警告阈值应小于快到期阈值');
        }
        if (thresholds.urgent < 1 || thresholds.urgent > 60) {
            errors.push('快到期阈值应在1-60天之间');
        }
        if (thresholds.critical < 1 || thresholds.critical > 30) {
            errors.push('紧急阈值应在1-30天之间');
        }
        if (thresholds.warning < 1 || thresholds.warning > 30) {
            errors.push('警告阈值应在1-30天之间');
        }

        return errors;
    }

    /**
     * 获取配置状态 (例如，未配置的联系人数量，缺失的短信设置等)
     */
    getConfigStatus() {
        const status = {
            smsConfigured: this.get('sms.ceic.account') && this.get('sms.ceic.password'),
            contactMappings: Object.keys(this.get('contacts.mapping', {})).length
        };
        return status;
    }
}

// 导出 ConfigManager 的实例，确保单例模式
const configManager = new ConfigManager();

// 暴露给全局，方便在 HTML 中直接调用
window.configManager = configManager;
