#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json
import os
from typing import Dict, Any, List, Optional
from werkzeug.datastructures import FileStorage

class DataProcessor:
    """数据处理工具类"""
    
    def __init__(self, upload_folder: str = 'uploads'):
        """
        初始化数据处理器
        
        Args:
            upload_folder: 文件上传目录
        """
        self.upload_folder = upload_folder
        os.makedirs(upload_folder, exist_ok=True)
    
    def process_uploaded_file(self, file: FileStorage) -> Dict[str, Any]:
        """
        处理上传的文件
        
        Args:
            file: 上传的文件
            
        Returns:
            dict: 处理结果
        """
        try:
            # 保存文件
            filename = file.filename
            file_path = os.path.join(self.upload_folder, filename)
            file.save(file_path)
            
            # 根据文件类型处理
            if filename.endswith('.csv'):
                return self._process_csv(file_path)
            elif filename.endswith(('.xlsx', '.xls')):
                return self._process_excel(file_path)
            else:
                return {
                    'success': False,
                    'message': '不支持的文件格式'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'文件处理失败: {str(e)}'
            }
    
    def _process_csv(self, file_path: str) -> Dict[str, Any]:
        """
        处理CSV文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 处理结果
        """
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                return {
                    'success': False,
                    'message': 'CSV文件编码不支持'
                }
            
            return self._process_dataframe(df, file_path)
            
        except Exception as e:
            return {
                'success': False,
                'message': f'CSV处理失败: {str(e)}'
            }
    
    def _process_excel(self, file_path: str) -> Dict[str, Any]:
        """
        处理Excel文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 处理结果
        """
        try:
            # 尝试不同的header位置
            header_positions = [0, 1, 2]
            df = None
            
            for header_pos in header_positions:
                try:
                    df = pd.read_excel(file_path, header=header_pos)
                    # 检查是否有有效数据
                    if not df.empty and len(df.columns) > 3:
                        break
                except:
                    continue
            
            if df is None or df.empty:
                return {
                    'success': False,
                    'message': 'Excel文件无法读取或无有效数据'
                }
            
            return self._process_dataframe(df, file_path)
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Excel处理失败: {str(e)}'
            }
    
    def _process_dataframe(self, df: pd.DataFrame, file_path: str) -> Dict[str, Any]:
        """
        处理DataFrame数据
        
        Args:
            df: pandas DataFrame
            file_path: 原始文件路径
            
        Returns:
            dict: 处理结果
        """
        try:
            # 清理数据
            df = self._clean_dataframe(df)
            
            # 转换为字典列表
            data_list = []
            for _, row in df.iterrows():
                item = {}
                for col in df.columns:
                    value = row[col]
                    if pd.notna(value):
                        item[col] = str(value).strip()
                    else:
                        item[col] = ""
                data_list.append(item)
            
            # 生成统计信息
            stats = self._generate_statistics(data_list)
            
            # 保存处理后的数据
            output_file = self._save_processed_data(data_list, file_path)
            
            return {
                'success': True,
                'message': '文件处理成功',
                'data': {
                    'columns': list(df.columns),
                    'records': data_list,
                    'total_count': len(data_list),
                    'statistics': stats,
                    'output_file': output_file
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'数据处理失败: {str(e)}'
            }
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理DataFrame数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            pd.DataFrame: 清理后的DataFrame
        """
        # 删除全空行和全空列
        df = df.dropna(how='all')
        df = df.dropna(axis=1, how='all')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        # 清理列名
        df.columns = [str(col).strip() for col in df.columns]
        
        return df
    
    def _generate_statistics(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成数据统计信息
        
        Args:
            data_list: 数据列表
            
        Returns:
            dict: 统计信息
        """
        if not data_list:
            return {}
        
        stats = {
            'total_records': len(data_list),
            'columns_count': len(data_list[0].keys()) if data_list else 0,
            'column_stats': {}
        }
        
        # 统计每列的信息
        for col in data_list[0].keys():
            values = [item.get(col, '') for item in data_list]
            non_empty_values = [v for v in values if v and str(v).strip()]
            
            stats['column_stats'][col] = {
                'total': len(values),
                'non_empty': len(non_empty_values),
                'empty': len(values) - len(non_empty_values),
                'empty_rate': (len(values) - len(non_empty_values)) / len(values) if values else 0
            }
        
        return stats
    
    def _save_processed_data(self, data_list: List[Dict[str, Any]], original_file: str) -> str:
        """
        保存处理后的数据
        
        Args:
            data_list: 数据列表
            original_file: 原始文件路径
            
        Returns:
            str: 输出文件路径
        """
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(original_file))[0]
        output_file = os.path.join(self.upload_folder, f'{base_name}_processed.json')
        
        # 保存为JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data_list, f, ensure_ascii=False, indent=2)
        
        return output_file
    
    def filter_data(self, data_list: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        过滤数据
        
        Args:
            data_list: 原始数据列表
            filters: 过滤条件
            
        Returns:
            list: 过滤后的数据
        """
        if not filters:
            return data_list
        
        filtered_data = data_list.copy()
        
        # 按状态过滤
        if 'status' in filters:
            status = filters['status']
            if status == 'completed':
                # 过滤已完成的任务
                filtered_data = [item for item in filtered_data if self._is_completed(item)]
            elif status == 'incomplete':
                # 过滤未完成的任务
                filtered_data = [item for item in filtered_data if not self._is_completed(item)]
            elif status == 'urgent':
                # 过滤快到期的任务
                filtered_data = [item for item in filtered_data if self._is_urgent(item)]
        
        # 按部门过滤
        if 'department' in filters and filters['department']:
            dept = filters['department']
            filtered_data = [item for item in filtered_data if self._match_department(item, dept)]
        
        # 按类型过滤
        if 'type' in filters and filters['type']:
            task_type = filters['type']
            filtered_data = [item for item in filtered_data if self._match_type(item, task_type)]
        
        return filtered_data
    
    def _is_completed(self, item: Dict[str, Any]) -> bool:
        """判断任务是否已完成"""
        # 根据实际数据结构调整判断逻辑
        status_fields = ['状态', '完成状态', '进度', 'status']
        for field in status_fields:
            if field in item:
                value = str(item[field]).lower()
                if '完成' in value or 'completed' in value or '100%' in value:
                    return True
        return False
    
    def _is_urgent(self, item: Dict[str, Any]) -> bool:
        """判断任务是否快到期"""
        # 根据实际数据结构调整判断逻辑
        deadline_fields = ['截止时间', '完成时限', 'deadline']
        for field in deadline_fields:
            if field in item and item[field]:
                # 这里可以添加日期比较逻辑
                pass
        return False
    
    def _match_department(self, item: Dict[str, Any], department: str) -> bool:
        """匹配部门"""
        dept_fields = ['牵头部门', '责任部门', 'department']
        for field in dept_fields:
            if field in item and department in str(item[field]):
                return True
        return False
    
    def _match_type(self, item: Dict[str, Any], task_type: str) -> bool:
        """匹配任务类型"""
        type_fields = ['整改类型', '任务类型', 'type']
        for field in type_fields:
            if field in item and task_type in str(item[field]):
                return True
        return False
