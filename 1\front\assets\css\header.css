/* 页面头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 头部操作按钮 */
.header-actions {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.settings-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 控制面板样式 */
.dashboard-controls {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.controls-row {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-weight: bold;
    color: #555;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2em;
    }

    .header-actions {
        position: static;
        transform: none;
        margin-top: 15px;
    }

    .controls-row {
        flex-direction: column;
        align-items: stretch;
    }
}
