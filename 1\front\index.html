<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纪委办智能化督办看板</title>

    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/header.css">
    <link rel="stylesheet" href="components/task-card/task-card.css">
    <link rel="stylesheet" href="components/modal/modal.css">
    <link rel="stylesheet" href="components/filter/filter.css">
    <link rel="stylesheet" href="components/statistics/statistics.css">
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <h1>纪委办智能化督办看板</h1>
        <p>多维度数据分析与智能督办提醒系统</p>
        <div class="header-actions">
            <button onclick="window.location.href='settings.html'" class="settings-btn">
                ⚙️ 系统设置
            </button>
        </div>
    </div>

    <div class="container">
        <!-- 控制面板 -->
        <div class="dashboard-controls">
            <div class="controls-row">
                <div class="control-group">
                    <label for="fileInput">选择数据文件:</label>
                    <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" style="padding: 8px;">
                </div>

                <button onclick="loadData()">加载数据</button>
                <button onclick="exportReport()">导出报告</button>
                <button onclick="batchReminder()" class="reminder-btn">批量督办提醒</button>
            </div>
            <div class="controls-row" style="margin-top: 15px;">
                <div class="control-group" style="width: 100%;">
                    <label for="customSmsContent" style="margin-bottom: 5px; display: block;">自定义短信内容 (可选):</label>
                    <textarea id="customSmsContent" rows="3" placeholder="输入自定义短信内容，为空时将发送默认提醒信息..." style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ccc; box-sizing: border-box;"></textarea>
                </div>
            </div>
        </div>

        <!-- 过滤标签 -->
        <div class="filter-tags" id="filterTags">
            <div class="filter-tag active" data-filter="all">
                <span>全部</span>
                <span class="filter-count" id="allCount">0</span>
            </div>
            <div class="filter-tag" data-filter="completed">
                <span>已完成</span>
                <span class="filter-count" id="completedFilterCount">0</span>
            </div>
            <div class="filter-tag" data-filter="incomplete">
                <span>未完成</span>
                <span class="filter-count" id="incompleteFilterCount">0</span>
            </div>
            <div class="filter-tag" data-filter="urgent">
                <span>🚨 快到期</span>
                <span class="filter-count" id="urgentFilterCount">0</span>
            </div>
            <div class="filter-tag" data-filter="overdue">
                <span>⚠️ 已过期</span>
                <span class="filter-count" id="overdueFilterCount">0</span>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="stats-panel" id="statsPanel">
            <div class="stats-title">项目分类统计</div>

            <!-- 总体统计 -->
            <div class="stats-summary">
                <div class="total-count" id="totalCount">0</div>
                <div class="total-label">督办任务总数</div>
            </div>

            <!-- 牵头部门统计 -->
            <div class="department-stats">
                <div class="category-title">按牵头部门统计</div>
                <div class="stats-grid" id="departmentStats">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 整改类型统计 -->
            <div class="type-stats">
                <div class="category-title">按整改类型统计</div>
                <div class="stats-grid" id="typeStats">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="tasks-grid" id="tasksContainer">
            <div class="loading">正在加载数据...</div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div class="detail-modal" id="detailModal">
        <div class="detail-modal-content">
            <div class="detail-modal-header">
                <h3 class="detail-modal-title" id="detailModalTitle">任务详情</h3>
                <button class="detail-close-btn" onclick="closeDetailModal()">×</button>
            </div>

            <div class="detail-section">
                <div class="detail-section-title">已完成工作及取得的成效</div>
                <div class="detail-section-content" id="completedWork">
                    暂无信息
                </div>
            </div>

            <div class="detail-section">
                <div class="detail-section-title">下一步工作计划</div>
                <div class="detail-section-content" id="nextPlan">
                    暂无信息
                </div>
            </div>
        </div>
    </div>

    <!-- 提醒弹窗 -->
    <div class="reminder-modal" id="reminderModal">
        <div class="reminder-modal-content">
            <div class="reminder-modal-header">
                <h3 class="reminder-modal-title" id="reminderModalTitle">督办提醒</h3>
                <button class="reminder-close-btn" onclick="closeReminderModal()">×</button>
            </div>
            <div class="reminder-modal-body">
                <textarea id="reminderContent" rows="10" class="reminder-textarea"></textarea>
            </div>
            <div class="reminder-modal-footer">
                <button id="sendReminderSmsBtn" class="send-btn">发送</button>
                <button onclick="closeReminderModal()" class="cancel-btn">取消</button>
            </div>
        </div>
    </div>

    <!-- 外部依赖 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- 配置文件 -->
    <script src="config/app-config.js"></script>

    <!-- 组件脚本 -->
    <script src="components/task-card/task-card.js"></script>
    <script src="components/modal/modal.js"></script>
    <script src="components/filter/filter.js"></script>
    <script src="components/statistics/statistics.js"></script>

    <!-- 工具脚本 -->
    <script src="utils/config-manager.js"></script>
    <script src="utils/state-manager.js"></script>
    <script src="utils/backend-service.js"></script>
    <script src="utils/sms-service.js"></script>
    <script src="utils/data-processor.js"></script>
    <script src="utils/file-handler.js"></script>
    <script src="utils/task-actions.js"></script>

    <!-- 主控制器 -->
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
