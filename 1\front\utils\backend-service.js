/**
 * 后端服务管理工具
 * 负责检测、启动和监控Python后端服务
 */

class BackendService {
    constructor() {
        this.baseUrl = 'http://localhost:3000';
        this.isChecking = false;
        this.isStarting = false;
        this.checkInterval = null;
        this.maxRetries = 3;
        this.retryDelay = 2000; // 2秒

        // 状态回调
        this.onStatusChange = null;

        // 初始化状态提示元素
        this.initStatusUI();
    }

    /**
     * 初始化状态提示UI
     */
    initStatusUI() {
        // 创建状态提示容器
        if (!document.getElementById('backend-status-container')) {
            const statusContainer = document.createElement('div');
            statusContainer.id = 'backend-status-container';
            statusContainer.innerHTML = `
                <div id="backend-status-modal" class="backend-status-modal" style="display: none;">
                    <div class="backend-status-content">
                        <div class="status-header">
                            <h3>后端服务状态</h3>
                            <button class="close-btn" onclick="window.backendService.hideStatus()">&times;</button>
                        </div>
                        <div class="status-body">
                            <div class="status-indicator">
                                <div id="status-icon" class="status-icon checking"></div>
                                <div id="status-text" class="status-text">检测服务状态...</div>
                            </div>
                            <div id="status-details" class="status-details"></div>
                            <div id="status-actions" class="status-actions" style="display: none;">
                                <button id="retry-btn" onclick="window.backendService.retryStart()">重试启动</button>
                                <button id="manual-btn" onclick="window.backendService.showManualInstructions()">手动启动</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(statusContainer);

            // 添加样式
            this.addStatusStyles();
        }
    }

    /**
     * 添加状态提示样式
     */
    addStatusStyles() {
        if (!document.getElementById('backend-status-styles')) {
            const styles = document.createElement('style');
            styles.id = 'backend-status-styles';
            styles.textContent = `
                .backend-status-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }

                .backend-status-content {
                    background: white;
                    border-radius: 8px;
                    padding: 0;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                }

                .status-header {
                    background: #f8f9fa;
                    padding: 15px 20px;
                    border-bottom: 1px solid #dee2e6;
                    border-radius: 8px 8px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .status-header h3 {
                    margin: 0;
                    color: #333;
                }

                .close-btn {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .close-btn:hover {
                    color: #333;
                }

                .status-body {
                    padding: 20px;
                }

                .status-indicator {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                }

                .status-icon {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    margin-right: 10px;
                    position: relative;
                }

                .status-icon.checking {
                    background: #ffc107;
                    animation: pulse 1.5s infinite;
                }

                .status-icon.starting {
                    background: #17a2b8;
                    animation: spin 1s linear infinite;
                }

                .status-icon.success {
                    background: #28a745;
                }

                .status-icon.error {
                    background: #dc3545;
                }

                .status-text {
                    font-weight: 500;
                    color: #333;
                }

                .status-details {
                    background: #f8f9fa;
                    padding: 10px;
                    border-radius: 4px;
                    font-size: 14px;
                    color: #666;
                    margin-bottom: 15px;
                    white-space: pre-line;
                }

                .status-actions {
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }

                .status-actions button {
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background: white;
                    cursor: pointer;
                    font-size: 14px;
                }

                .status-actions button:hover {
                    background: #f8f9fa;
                }

                #retry-btn {
                    background: #007bff !important;
                    color: white !important;
                    border-color: #007bff !important;
                }

                #retry-btn:hover {
                    background: #0056b3 !important;
                }

                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.5; }
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(styles);
        }
    }

    /**
     * 检测并启动后端服务
     */
    async checkAndStart() {
        if (this.isChecking || this.isStarting) {
            console.log('后端服务检测或启动正在进行中...');
            return;
        }

        this.showStatus();
        this.updateStatus('checking', '检测后端服务状态...', '正在尝试连接后端...');

        try {
            // 不再检测服务是否运行，直接尝试启动
            // const isRunning = await this.checkServiceStatus();
            // if (isRunning) {
            //     this.updateStatus('success', '后端服务已运行', '服务状态正常，所有API功能可用。');
            //     setTimeout(() => this.hideStatus(), 2000);
            //     return true;
            // }

            // 服务未运行（或未检测到），尝试启动
            return await this.startService();

        } catch (error) {
            console.error('后端服务启动失败:', error);
            this.updateStatus('error', '服务启动失败', `错误信息: ${error.message}`);
            this.showActions();
            return false;
        }
    }

    /**
     * 尝试启动服务
     */
    async attemptServiceStart() {
        // 在浏览器环境中，我们无法直接启动Python进程
        // 但我们可以尝试一些间接方式

        try {
            // 方法1: 尝试通过预设的启动端点（如果后端支持）
            const startResult = await this.tryStartViaAPI();
            if (startResult) {
                return true;
            }

            // 方法2: 检查是否有自动启动脚本在运行
            const autoStartResult = await this.checkAutoStartScript();
            if (autoStartResult) {
                return true;
            }

            // 方法3: 尝试通过文件系统API（如果支持）
            const fileSystemResult = await this.tryFileSystemStart();
            if (fileSystemResult) {
                return true;
            }

            // 如果所有方法都失败，返回false
            return false;

        } catch (error) {
            console.error('启动服务时发生错误:', error);
            return false;
        }
    }

    /**
     * 尝试通过API启动服务
     */
    async tryStartViaAPI() {
        try {
            // 某些后端可能提供启动端点
            const response = await fetch(`${this.baseUrl}/api/start`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    /**
     * 检查自动启动脚本
     */
    async checkAutoStartScript() {
        try {
            // 检查是否有状态文件表明自动启动脚本正在运行
            const response = await fetch('./backend_status.json');
            if (response.ok) {
                const status = await response.json();
                return status.status === 'starting' || status.status === 'running';
            }
        } catch (error) {
            // 文件不存在或无法访问
        }
        return false;
    }

    /**
     * 尝试通过文件系统启动（实验性）
     */
    async tryFileSystemStart() {
        // 这个方法在标准浏览器环境中无法实现
        // 但在某些特殊环境（如Electron、PWA等）中可能可用

        if (window.electronAPI) {
            // Electron环境
            try {
                return await window.electronAPI.startBackendService();
            } catch (error) {
                console.error('Electron启动失败:', error);
            }
        }

        if ('serviceWorker' in navigator) {
            // Service Worker环境（实验性）
            try {
                const registration = await navigator.serviceWorker.ready;
                if (registration.active) {
                    registration.active.postMessage({
                        type: 'START_BACKEND_SERVICE'
                    });
                    // 等待响应
                    await this.delay(3000);
                    return await this.checkServiceStatus();
                }
            } catch (error) {
                console.error('Service Worker启动失败:', error);
            }
        }

        return false;
    }

    /**
     * 等待服务就绪
     */
    async waitForServiceReady(maxWaitTime = 30000) {
        const startTime = Date.now();
        const checkInterval = 1000; // 每秒检查一次

        while (Date.now() - startTime < maxWaitTime) {
            try {
                const isRunning = await this.checkServiceStatus();
                if (isRunning) {
                    return true;
                }
            } catch (error) {
                // 继续等待
            }

            await this.delay(checkInterval);
            this.updateStatus('starting', '等待服务就绪...', `已等待 ${Math.floor((Date.now() - startTime) / 1000)} 秒...`);
        }

        return false;
    }

    /**
     * 显示状态提示
     */
    showStatus() {
        const modal = document.getElementById('backend-status-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    /**
     * 隐藏状态提示
     */
    hideStatus() {
        const modal = document.getElementById('backend-status-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * 更新状态显示
     */
    updateStatus(type, text, details = '') {
        const icon = document.getElementById('status-icon');
        const textEl = document.getElementById('status-text');
        const detailsEl = document.getElementById('status-details');

        if (icon) {
            icon.className = `status-icon ${type}`;
        }

        if (textEl) {
            textEl.textContent = text;
        }

        if (detailsEl) {
            detailsEl.textContent = details;
            detailsEl.style.display = details ? 'block' : 'none';
        }

        // 隐藏操作按钮
        this.hideActions();
    }

    /**
     * 显示操作按钮
     */
    showActions() {
        const actions = document.getElementById('status-actions');
        if (actions) {
            actions.style.display = 'flex';
        }
    }

    /**
     * 隐藏操作按钮
     */
    hideActions() {
        const actions = document.getElementById('status-actions');
        if (actions) {
            actions.style.display = 'none';
        }
    }

    /**
     * 重试启动
     */
    async retryStart() {
        this.hideActions();
        await this.checkAndStart();
    }

    /**
     * 显示手动启动说明
     */
    showManualInstructions() {
        const instructions = `
手动启动后端服务的方法：

方法一：使用启动脚本
• Windows: 双击运行 start_backend.bat
• Linux/Mac: 运行 ./start_backend.sh
• UV版本: 运行 start_backend_uv.bat 或 ./start_backend_uv.sh

方法二：命令行启动
1. 打开命令行/终端
2. 进入项目目录
3. 运行: python run.py

方法三：快速启动
运行: python quick_start.py

启动成功后，服务将运行在 http://localhost:3000
然后点击"重试启动"按钮重新检测服务状态。
        `;

        this.updateStatus('error', '需要手动启动服务', instructions.trim());
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取服务状态
     */
    async getServiceInfo() {
        // TODO: 后续可以考虑从后端获取更详细的服务信息，例如版本号、运行状态等
        return {};
    }

    /**
     * 开始监控服务状态（不再需要定期健康检查）
     * @param {number} interval - 检查间隔（毫秒）
     */
    startMonitoring(interval = 30000) {
        // 鉴于取消了健康检查，此处不再需要定时器来定期检测服务状态。
        // 服务状态将主要通过尝试启动或API调用结果来判断。
        console.log('不再定期监控后端服务状态。');
        // if (this.checkInterval) {
        //     clearInterval(this.checkInterval);
        // }
        // this.checkInterval = setInterval(async () => {
        //     console.log('定时检测后端服务状态...');
        //     const isRunning = await this.checkServiceStatus();
        //     if (isRunning) {
        //         this.updateStatus('success', '后端服务已运行', '服务状态正常。');
        //     } else {
        //         this.updateStatus('error', '后端服务未运行', '尝试重新启动...');
        //         this.startService();
        //     }
        // }, interval);
    }

    /**
     * 清理资源
     */
    destroy() {
        // this.stopMonitoring(); // 移除对已删除方法的调用
        // 其他清理工作
        if (document.getElementById('backend-status-container')) {
            document.getElementById('backend-status-container').remove();
        }
        if (document.getElementById('backend-status-styles')) {
            document.getElementById('backend-status-styles').remove();
        }
    }
}

// 导出实例
window.backendService = new BackendService();

// 页面加载完成后开始监控
document.addEventListener('DOMContentLoaded', () => {
    // 延迟启动监控，避免与其他初始化冲突
    setTimeout(() => {
        window.backendService.startMonitoring();
    }, 5000);
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.backendService) {
        window.backendService.destroy();
    }
});
