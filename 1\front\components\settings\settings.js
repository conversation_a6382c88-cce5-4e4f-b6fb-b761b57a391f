/**
 * 设置页面控制器
 */

class SettingsController {
    constructor() {
        this.currentTab = 'general';
        this.init();
    }

    /**
     * 初始化设置页面
     */
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeEventListeners();
            this.loadSettingsToForm();
            this.initializeSmsService();
            this.updateSmsStatus();
            this.initializeStateManager();
        });
    }

    /**
     * 初始化短信服务
     */
    initializeSmsService() {
        if (window.smsService) {
            window.smsService.initialize({
                ...window.AppConfig.sms.ceic,
                enabled: window.AppConfig.sms.settings.enabled,
                backendApiUrl: window.AppConfig.sms.backendApiUrl
            });
            console.log('设置页面：短信服务已初始化', window.AppConfig.sms.settings.enabled ? '已启用' : '未启用');
        }
    }

    /**
     * 初始化状态管理器
     */
    initializeStateManager() {
        if (window.stateManager) {
            // 在设置页面不自动初始化状态管理器，只是确保它可用
            console.log('设置页面：状态管理器可用');

            // 检查是否有可恢复的数据状态
            if (window.stateManager.hasRestorableState()) {
                console.log('设置页面：检测到可恢复的数据状态');
            }
        }
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });

        // 保存设置
        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        // 重置设置
        document.getElementById('resetSettingsBtn').addEventListener('click', () => {
            this.resetSettings();
        });

        // 导出设置
        document.getElementById('exportSettingsBtn').addEventListener('click', () => {
            this.exportSettings();
        });

        // 导入设置
        document.getElementById('importSettingsBtn').addEventListener('click', () => {
            document.getElementById('importSettingsInput').click();
        });

        document.getElementById('importSettingsInput').addEventListener('change', (e) => {
            this.importSettings(e.target.files[0]);
        });

        // 联系人管理事件
        document.getElementById('addContactBtn').addEventListener('click', () => {
            this.addContactMapping();
        });

        document.getElementById('autoDetectContactsBtn').addEventListener('click', () => {
            this.autoDetectContacts();
        });

        document.getElementById('parseContactsBtn').addEventListener('click', () => {
            this.parseContactsText();
        });

        document.getElementById('clearContactsBtn').addEventListener('click', () => {
            this.clearAllContacts();
        });

        // 测试短信
        document.getElementById('testSmsBtn').addEventListener('click', () => {
            this.testSms();
        });

        // 短信服务状态监听
        document.getElementById('smsEnabled').addEventListener('change', () => {
            this.updateSmsStatus();
        });

        // 实时保存（如果启用）
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('change', () => {
                if (window.AppConfig.features?.enableAutoSave) {
                    this.saveSettings();
                }
            });
        });
    }

    /**
     * 切换标签页
     * @param {string} tab - 标签页名称
     */
    switchTab(tab) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // 更新面板显示
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${tab}-panel`).classList.add('active');

        this.currentTab = tab;
    }

    /**
     * 从本地存储加载设置
     * @returns {Object} 设置对象
     */
    loadSettings() {
        return JSON.parse(JSON.stringify(window.AppConfig));
    }

    /**
     * 将设置加载到表单
     */
    loadSettingsToForm() {
        // 常规设置 - 直接从 AppConfig 获取值
        document.getElementById('urgentThreshold').value = window.AppConfig.time?.urgentThreshold || 15;
        document.getElementById('criticalThreshold').value = window.AppConfig.time?.criticalThreshold || 3;
        document.getElementById('warningThreshold').value = window.AppConfig.time?.warningThreshold || 7;
        document.getElementById('maxEmptyFields').value = window.AppConfig.data?.maxEmptyFields || 4;
        document.getElementById('enableStatisticsFilter').checked = window.AppConfig.features?.enableStatisticsFilter !== false;
        document.getElementById('enableAutoSave').checked = window.AppConfig.features?.enableAutoSave === true;

        // 短信设置
        document.getElementById('smsEnabled').checked = window.AppConfig.sms?.settings?.enabled === true;
        document.getElementById('backendApiUrl').value = window.AppConfig.sms?.backendApiUrl || '';
        document.getElementById('ceicAccount').value = window.AppConfig.sms?.ceic?.account || '';
        document.getElementById('ceicPassword').value = window.AppConfig.sms?.ceic?.password || '';
        document.getElementById('batchInterval').value = window.AppConfig.sms?.settings?.batchInterval || 1000;
        document.getElementById('maxRetries').value = window.AppConfig.sms?.settings?.maxRetries || 3;

        // 联系人设置
        document.getElementById('enablePhoneValidation').checked = window.AppConfig.contacts?.settings?.phoneValidation !== false;
        document.getElementById('autoExtractContacts').checked = window.AppConfig.contacts?.settings?.autoExtractFromData !== false;
        this.loadContactMappings();

        // 提醒设置
        document.getElementById('enableBrowserNotification').checked = window.AppConfig.features?.enableBrowserNotification !== false;
        document.getElementById('enableSmsNotification').checked = window.AppConfig.sms?.settings?.enabled === true;
        document.getElementById('reminderDays').value = window.AppConfig.time?.urgentThreshold || 7;
        document.getElementById('dailyReminderTime').value = window.AppConfig.time?.dailyReminderTime || '09:00';
    }

    /**
     * 从表单收集设置
     * @returns {Object} 设置对象
     */
    collectSettingsFromForm() {
        return {
            time: {
                urgentThreshold: parseInt(document.getElementById('urgentThreshold').value),
                criticalThreshold: parseInt(document.getElementById('criticalThreshold').value),
                warningThreshold: parseInt(document.getElementById('warningThreshold').value),
                dailyReminderTime: document.getElementById('dailyReminderTime').value
            },
            data: {
                maxEmptyFields: parseInt(document.getElementById('maxEmptyFields').value)
            },
            features: {
                enableStatisticsFilter: document.getElementById('enableStatisticsFilter').checked,
                enableAutoSave: document.getElementById('enableAutoSave').checked,
                enableBrowserNotification: document.getElementById('enableBrowserNotification').checked,
                enableSmsNotification: document.getElementById('enableSmsNotification').checked
            },
            contacts: {
                mapping: this.collectContactMappings(),
                settings: {
                    phoneValidation: document.getElementById('enablePhoneValidation').checked,
                    autoExtractFromData: document.getElementById('autoExtractContacts').checked
                }
            },
            sms: {
                backendApiUrl: document.getElementById('backendApiUrl').value.trim(),
                settings: {
                    enabled: document.getElementById('smsEnabled').checked,
                    batchInterval: parseInt(document.getElementById('batchInterval').value),
                    maxRetries: parseInt(document.getElementById('maxRetries').value)
                },
                ceic: {
                    account: document.getElementById('ceicAccount').value.trim(),
                    password: document.getElementById('ceicPassword').value.trim()
                }
            }
        };
    }

    /**
     * 保存设置
     */
    saveSettings() {
        try {
            const newSettings = this.collectSettingsFromForm();

            // 验证常规设置阈值
            const errors = this.validateThresholds(newSettings.time);
            if (errors.length > 0) {
                this.showMessage('配置错误：' + errors.join(', '), 'error');
                return;
            }

            // 使用 configManager 批量更新 AppConfig
            window.configManager.setMultiple(newSettings);
            window.configManager.saveConfig();

            // 重新初始化短信服务（使用已更新的 AppConfig）
            this.initializeSmsService();

            // 更新状态显示
            this.updateSmsStatus();

            // 触发任务列表重新渲染（如果在主页面）
            if (window.dashboard && window.dashboard.updateDashboard) {
                window.dashboard.updateDashboard();
            }

            this.showMessage('设置保存成功！', 'success');
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showMessage('保存设置失败：' + error.message, 'error');
        }
    }

    /**
     * 验证阈值设置
     */
    validateThresholds(timeConfig) {
        const errors = [];

        if (timeConfig.criticalThreshold >= timeConfig.warningThreshold) {
            errors.push('紧急阈值应小于警告阈值');
        }
        if (timeConfig.warningThreshold >= timeConfig.urgentThreshold) {
            errors.push('警告阈值应小于快到期阈值');
        }
        if (timeConfig.urgentThreshold < 1 || timeConfig.urgentThreshold > 60) {
            errors.push('快到期阈值应在1-60天之间');
        }
        if (timeConfig.criticalThreshold < 1 || timeConfig.criticalThreshold > 30) {
            errors.push('紧急阈值应在1-30天之间');
        }
        if (timeConfig.warningThreshold < 1 || timeConfig.warningThreshold > 30) {
            errors.push('警告阈值应在1-30天之间');
        }

        return errors;
    }

    /**
     * 重置设置
     */
    resetSettings() {
        if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            try {
                window.configManager.reset();
                this.loadSettingsToForm();
                this.initializeSmsService();
                this.updateSmsStatus();
                this.showMessage('设置已重置为默认值', 'success');
            } catch (error) {
                console.error('重置设置失败:', error);
                this.showMessage('重置设置失败：' + error.message, 'error');
            }
        }
    }

    /**
     * 导出设置
     */
    exportSettings() {
        try {
            const dataStr = window.configManager.export();
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `dashboard-settings-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            this.showMessage('设置导出成功', 'success');
        } catch (error) {
            console.error('导出设置失败:', error);
            this.showMessage('导出设置失败：' + error.message, 'error');
        }
    }

    /**
     * 导入设置
     * @param {File} file - 设置文件
     */
    async importSettings(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const success = window.configManager.import(text);

            if (success) {
                this.loadSettingsToForm();
                this.initializeSmsService();
                this.updateSmsStatus();
                this.showMessage('设置导入成功', 'success');
            } else {
                throw new Error('导入配置失败');
            }
        } catch (error) {
            console.error('导入设置失败:', error);
            this.showMessage('导入设置失败：' + error.message, 'error');
        }
    }

    /**
     * 测试短信发送
     */
    async testSms() {
        const phoneNumber = document.getElementById('testPhoneNumber').value.trim();

        if (!phoneNumber) {
            this.showMessage('请输入测试手机号', 'warning');
            return;
        }

        if (!window.smsService) {
            this.showMessage('短信服务未初始化', 'error');
            return;
        }

        const testBtn = document.getElementById('testSmsBtn');
        testBtn.disabled = true;
        testBtn.textContent = '发送中...';

        try {
            // 先保存当前设置
            this.saveSettings();

            // 发送测试短信
            const result = await window.smsService.testSms(phoneNumber);

            if (result.success) {
                this.showMessage('测试短信发送成功', 'success');
            } else {
                this.showMessage('测试短信发送失败：' + result.message, 'error');
            }
        } catch (error) {
            console.error('测试短信失败:', error);
            this.showMessage('测试短信失败：' + error.message, 'error');
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = '发送测试短信';
        }
    }

    /**
     * 更新短信服务状态
     */
    updateSmsStatus() {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        const testBtn = document.getElementById('testSmsBtn');

        const smsEnabled = window.AppConfig.sms?.settings?.enabled;
        const hasConfig = window.configManager.get('sms.ceic.account') && window.configManager.get('sms.ceic.password');

        if (!smsEnabled) {
            statusIndicator.className = 'status-indicator';
            statusText.textContent = '服务已禁用';
            testBtn.disabled = true;
        } else if (!hasConfig) {
            statusIndicator.className = 'status-indicator warning';
            statusText.textContent = '配置不完整';
            testBtn.disabled = true;
        } else {
            statusIndicator.className = 'status-indicator success';
            statusText.textContent = '服务已就绪';
            testBtn.disabled = false;
        }
    }

    /**
     * 验证短信配置 (现在直接通过 configManager 获取AppConfig中的值)
     * @returns {boolean} 配置是否完整
     */
    validateSmsConfig() {
        return window.configManager.get('sms.ceic.account') && window.configManager.get('sms.ceic.password');
    }

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;

        // 添加样式
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });

        // 设置背景色
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        messageEl.style.backgroundColor = colors[type] || colors.info;

        // 添加到页面
        document.body.appendChild(messageEl);

        // 显示动画
        setTimeout(() => {
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 加载联系人映射表
     */
    loadContactMappings() {
        const contactMapping = window.AppConfig.contacts?.mapping || {};
        const container = document.getElementById('contactMappingList');
        container.innerHTML = '';

        // 如果没有联系人，添加一个空的
        if (Object.keys(contactMapping).length === 0) {
            this.addContactMapping('', '');
        } else {
            // 加载现有联系人
            for (const [name, phone] of Object.entries(contactMapping)) {
                this.addContactMapping(name, phone);
            }
        }

        this.updateContactStatus();
    }

    /**
     * 添加联系人映射项
     * @param {string} name - 联系人姓名
     * @param {string} phone - 手机号
     */
    addContactMapping(name = '', phone = '') {
        const container = document.getElementById('contactMappingList');
        const item = document.createElement('div');
        item.className = 'contact-mapping-item';

        item.innerHTML = `
            <input type="text" class="contact-name-input" placeholder="请输入部门名称" value="${name}">
            <input type="tel" class="contact-phone-input" placeholder="请输入负责人手机号" value="${phone}">
            <button type="button" class="contact-remove-btn">删除</button>
        `;

        // 添加事件监听
        const nameInput = item.querySelector('.contact-name-input');
        const phoneInput = item.querySelector('.contact-phone-input');
        const removeBtn = item.querySelector('.contact-remove-btn');

        nameInput.addEventListener('input', () => this.updateContactStatus());
        phoneInput.addEventListener('input', () => {
            this.validatePhoneInput(phoneInput);
            this.updateContactStatus();
        });
        removeBtn.addEventListener('click', () => {
            item.remove();
            this.updateContactStatus();
        });

        container.appendChild(item);
        this.updateContactStatus();
    }

    /**
     * 验证手机号输入
     * @param {HTMLInputElement} input - 手机号输入框
     */
    validatePhoneInput(input) {
        const phone = input.value.trim();
        const isValid = /^1[3-9]\d{9}$/.test(phone);

        if (phone && !isValid) {
            input.classList.add('invalid');
            input.parentElement.classList.add('invalid');
        } else {
            input.classList.remove('invalid');
            input.parentElement.classList.remove('invalid');
        }
    }

    /**
     * 收集联系人映射数据
     * @returns {Object} 联系人映射对象
     */
    collectContactMappings() {
        const mappings = {};
        const items = document.querySelectorAll('.contact-mapping-item');

        items.forEach(item => {
            const name = item.querySelector('.contact-name-input').value.trim();
            const phone = item.querySelector('.contact-phone-input').value.trim();

            if (name && phone) {
                mappings[name] = phone;
            }
        });

        return mappings;
    }

    /**
     * 自动检测联系人
     */
    autoDetectContacts() {
        // 根据配置的联系人类型决定检测方式
        const contactType = window.AppConfig.contacts?.settings?.contactType || 'department';
        const contacts = new Set();

        // 从默认配置中获取
        if (window.AppConfig.contacts?.mapping) {
            Object.keys(window.AppConfig.contacts.mapping).forEach(name => {
                if (name) contacts.add(name);
            });
        }

        // 如果有全局数据，从中提取
        if (window.filteredTasks) {
            window.filteredTasks.forEach(task => {
                if (contactType === 'department') {
                    // 提取牵头部门
                    const department = task['牵头整改部门'];
                    if (department && department !== '未提供') {
                        contacts.add(department);
                    }
                } else {
                    // 提取责任领导
                    const responsible = task['责任领导'];
                    if (responsible && responsible !== '未提供') {
                        contacts.add(responsible);
                    }
                }
            });
        }

        // 清空现有列表
        document.getElementById('contactMappingList').innerHTML = '';

        // 添加检测到的联系人
        if (contacts.size > 0) {
            contacts.forEach(name => {
                const existingPhone = window.AppConfig.contacts?.mapping?.[name] || '';
                this.addContactMapping(name, existingPhone);
            });
            const contactTypeName = contactType === 'department' ? '部门' : '责任领导';
            this.showMessage(`自动检测到 ${contacts.size} 个${contactTypeName}`, 'success');
        } else {
            this.addContactMapping();
            this.showMessage('未检测到联系人，请手动添加', 'warning');
        }
    }

    /**
     * 解析联系人文本
     */
    parseContactsText() {
        const text = document.getElementById('contactsTextArea').value.trim();
        if (!text) {
            this.showMessage('请输入联系人信息', 'warning');
            return;
        }

        const lines = text.split('\n');
        const contacts = [];
        let errorCount = 0;

        lines.forEach((line, index) => {
            line = line.trim();
            if (!line) return;

            // 支持多种分隔符：冒号、逗号、空格
            let name, phone;
            if (line.includes(':')) {
                [name, phone] = line.split(':');
            } else if (line.includes(',')) {
                [name, phone] = line.split(',');
            } else if (line.includes(' ')) {
                [name, phone] = line.split(/\s+/);
            } else {
                errorCount++;
                return;
            }

            name = name?.trim();
            phone = phone?.trim();

            if (name && phone) {
                // 验证手机号格式
                if (/^1[3-9]\d{9}$/.test(phone)) {
                    contacts.push({ name, phone });
                } else {
                    errorCount++;
                }
            } else {
                errorCount++;
            }
        });

        if (contacts.length > 0) {
            // 清空现有列表
            document.getElementById('contactMappingList').innerHTML = '';

            // 添加解析的联系人
            contacts.forEach(contact => {
                this.addContactMapping(contact.name, contact.phone);
            });

            let message = `成功导入 ${contacts.length} 个联系人`;
            if (errorCount > 0) {
                message += `，${errorCount} 行格式错误已跳过`;
            }
            this.showMessage(message, 'success');

            // 清空文本框
            document.getElementById('contactsTextArea').value = '';
        } else {
            this.showMessage('没有找到有效的联系人信息，请检查格式', 'error');
        }
    }

    /**
     * 清空所有联系人
     */
    clearAllContacts() {
        if (confirm('确定要清空所有联系人吗？')) {
            document.getElementById('contactMappingList').innerHTML = '';
            document.getElementById('contactsTextArea').value = '';
            this.addContactMapping(); // 添加一个空的
            this.showMessage('已清空所有联系人', 'success');
        }
    }

    /**
     * 更新联系人状态
     */
    updateContactStatus() {
        const mappings = this.collectContactMappings();
        const configuredCount = Object.keys(mappings).length;
        const validPhones = Object.values(mappings).filter(phone => /^1[3-9]\d{9}$/.test(phone)).length;

        // 计算配置完整度
        const totalResponsible = this.getTotalResponsiblePersons();
        const completeness = (window.AppConfig.contacts?.settings?.autoExtractFromData === false && totalResponsible === 0) ? 100 : 
                             (totalResponsible > 0 ? Math.round((configuredCount / totalResponsible) * 100) : 0);

        document.getElementById('configuredContactsCount').textContent = configuredCount;
        document.getElementById('validPhonesCount').textContent = validPhones;
        document.getElementById('configCompleteness').textContent = completeness + '%';
    }

    /**
     * 获取总的联系人数量
     * @returns {number} 联系人总数
     */
    getTotalResponsiblePersons() {
        const contactType = window.AppConfig.contacts?.settings?.contactType || 'department';
        const contacts = new Set();

        // 从默认配置中获取
        if (window.AppConfig.contacts?.mapping) {
            Object.keys(window.AppConfig.contacts.mapping).forEach(name => {
                if (name) contacts.add(name);
            });
        }

        // 从当前数据中获取
        if (window.filteredTasks) {
            window.filteredTasks.forEach(task => {
                if (contactType === 'department') {
                    // 统计牵头部门
                    const department = task['牵头整改部门'];
                    if (department && department !== '未提供') {
                        contacts.add(department);
                    }
                } else {
                    // 统计责任领导
                    const responsible = task['责任领导'];
                    if (responsible && responsible !== '未提供') {
                        contacts.add(responsible);
                    }
                }
            });
        }

        return contacts.size;
    }
}

// 初始化设置控制器
const settingsController = new SettingsController();
