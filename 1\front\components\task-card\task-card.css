/* 任务卡片样式 */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.task-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 5px solid #667eea;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 300px;
}

.task-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.task-card.urgent {
    border-left-color: #e74c3c;
    background: linear-gradient(135deg, #fff 0%, #ffebee 100%);
    border: 2px solid #ffcdd2;
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.15);
}

.task-card.urgent:hover {
    border-color: #e74c3c;
    box-shadow: 0 5px 20px rgba(231, 76, 60, 0.25);
}

.task-card.completed {
    border-left-color: #27ae60;
    background: linear-gradient(135deg, #fff 0%, #e8f5e8 100%);
}

.task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.task-title {
    font-size: 1.2em;
    font-weight: bold;
    color: #333;
}

.task-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: bold;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-incomplete {
    background: #f8d7da;
    color: #721c24;
}

.status-urgent {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #e74c3c;
    font-weight: bold;
    animation: urgentPulse 2s infinite;
}

@keyframes urgentPulse {
    0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
    100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

/* 不同紧急程度的任务卡片样式 */
.task-card.critical {
    border-left-color: #d32f2f;
    background: linear-gradient(135deg, #fff 0%, #ffebee 100%);
    border: 2px solid #f44336;
    box-shadow: 0 2px 15px rgba(211, 47, 47, 0.2);
    animation: criticalBlink 1s infinite alternate;
}

.task-card.overdue {
    border-left-color: #b71c1c;
    background: linear-gradient(135deg, #fff 0%, #ffcdd2 100%);
    border: 2px solid #d32f2f;
    box-shadow: 0 2px 15px rgba(183, 28, 28, 0.3);
}

@keyframes criticalBlink {
    0% { border-color: #f44336; }
    100% { border-color: #d32f2f; }
}

/* 不同紧急程度的状态标签 */
.status-critical {
    background: #ffcdd2;
    color: #b71c1c;
    border: 1px solid #d32f2f;
    font-weight: bold;
    animation: criticalPulse 1.5s infinite;
}

.status-overdue {
    background: #d32f2f;
    color: white;
    border: 1px solid #b71c1c;
    font-weight: bold;
    animation: overduePulse 1s infinite;
}

@keyframes criticalPulse {
    0% { box-shadow: 0 0 0 0 rgba(211, 47, 47, 0.5); }
    70% { box-shadow: 0 0 0 8px rgba(211, 47, 47, 0); }
    100% { box-shadow: 0 0 0 0 rgba(211, 47, 47, 0); }
}

@keyframes overduePulse {
    0% { background: #d32f2f; }
    50% { background: #b71c1c; }
    100% { background: #d32f2f; }
}

.task-details {
    margin-bottom: 15px;
}

.task-detail {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
}

.task-detail strong {
    min-width: 100px;
    color: #555;
}

/* 任务操作按钮 */
.task-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    min-height: 50px;
}

.task-actions .reminder-btn {
    margin-right: auto;
}

.reminder-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.reminder-btn:hover {
    transform: scale(1.05);
}

.detail-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.detail-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .task-card {
        min-height: 280px;
    }

    .task-actions {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .task-actions .reminder-btn {
        margin-right: 0;
        order: 1;
    }

    .detail-btn {
        order: 2;
    }
}
