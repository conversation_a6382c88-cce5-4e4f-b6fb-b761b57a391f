#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os
from typing import List, Dict, Any, Optional
from werkzeug.datastructures import FileStorage

def validate_phone_number(phone: str) -> bool:
    """
    验证中国大陆手机号格式
    
    Args:
        phone: 手机号字符串
        
    Returns:
        bool: 是否为有效手机号
    """
    if not phone or not isinstance(phone, str):
        return False
    
    # 中国大陆手机号正则表达式
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone.strip()))

def validate_phone_numbers(phones: List[str]) -> Dict[str, Any]:
    """
    批量验证手机号
    
    Args:
        phones: 手机号列表
        
    Returns:
        dict: 验证结果
    """
    if not phones or not isinstance(phones, list):
        return {
            'valid': False,
            'message': '手机号列表不能为空',
            'valid_phones': [],
            'invalid_phones': []
        }
    
    valid_phones = []
    invalid_phones = []
    
    for phone in phones:
        if validate_phone_number(phone):
            valid_phones.append(phone.strip())
        else:
            invalid_phones.append(phone)
    
    return {
        'valid': len(valid_phones) > 0,
        'message': f'有效手机号: {len(valid_phones)}, 无效手机号: {len(invalid_phones)}',
        'valid_phones': valid_phones,
        'invalid_phones': invalid_phones,
        'total': len(phones),
        'valid_count': len(valid_phones),
        'invalid_count': len(invalid_phones)
    }

def validate_sms_params(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证短信发送参数
    
    Args:
        data: 请求数据
        
    Returns:
        dict: 验证结果
    """
    errors = []
    
    # 验证必要参数
    required_fields = ['phoneNumber', 'signName', 'templateCode']
    for field in required_fields:
        if field not in data or not data[field]:
            errors.append(f'缺少必要参数: {field}')
    
    # 验证手机号
    if 'phoneNumber' in data:
        if isinstance(data['phoneNumber'], list):
            # 批量发送
            phone_validation = validate_phone_numbers(data['phoneNumber'])
            if not phone_validation['valid']:
                errors.append('没有有效的手机号')
        else:
            # 单条发送
            if not validate_phone_number(data['phoneNumber']):
                errors.append('手机号格式不正确')
    
    # 验证签名名称
    if 'signName' in data and data['signName']:
        if len(data['signName']) > 12:
            errors.append('签名名称不能超过12个字符')
    
    # 验证模板代码
    if 'templateCode' in data and data['templateCode']:
        if not re.match(r'^SMS_\d+$', data['templateCode']):
            errors.append('模板代码格式不正确')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'message': '; '.join(errors) if errors else '参数验证通过'
    }

def validate_file_upload(file: FileStorage, allowed_extensions: set) -> Dict[str, Any]:
    """
    验证上传文件
    
    Args:
        file: 上传的文件
        allowed_extensions: 允许的文件扩展名
        
    Returns:
        dict: 验证结果
    """
    if not file or not file.filename:
        return {
            'valid': False,
            'message': '没有选择文件'
        }
    
    # 检查文件扩展名
    filename = file.filename.lower()
    extension = filename.rsplit('.', 1)[1] if '.' in filename else ''
    
    if extension not in allowed_extensions:
        return {
            'valid': False,
            'message': f'不支持的文件格式，支持的格式: {", ".join(allowed_extensions)}'
        }
    
    # 检查文件大小（通过seek获取）
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)  # 重置文件指针
    
    max_size = 16 * 1024 * 1024  # 16MB
    if file_size > max_size:
        return {
            'valid': False,
            'message': f'文件大小超过限制 ({max_size / 1024 / 1024:.1f}MB)'
        }
    
    return {
        'valid': True,
        'message': '文件验证通过',
        'filename': file.filename,
        'extension': extension,
        'size': file_size
    }

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除危险字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    if not filename:
        return 'unnamed_file'
    
    # 移除路径分隔符和其他危险字符
    dangerous_chars = ['/', '\\', '..', '<', '>', ':', '"', '|', '?', '*']
    clean_name = filename
    
    for char in dangerous_chars:
        clean_name = clean_name.replace(char, '_')
    
    # 限制文件名长度
    if len(clean_name) > 255:
        name, ext = os.path.splitext(clean_name)
        clean_name = name[:255-len(ext)] + ext
    
    return clean_name
