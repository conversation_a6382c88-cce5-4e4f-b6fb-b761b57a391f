/**
 * CEIC 短信服务工具
 * 基于中电联短信服务API实现短信发送功能
 */

class SmsService {
    constructor() {
        this.config = AppConfig.sms;
        this.isInitialized = false;
        console.log('SmsService: 构造函数中 this.config.ceic:', this.config.ceic);
    }

    /**
     * 初始化短信服务
     * @param {Object} settings - 短信服务设置
     */
    initialize(settings = {}) {
        // 检查是否有配置变化
        const hasChanges = this.hasConfigChanges(settings);

        // 更新配置
        if (settings.account) this.config.ceic.account = settings.account;
        if (settings.password) this.config.ceic.password = settings.password;
        if (settings.enabled !== undefined) this.config.settings.enabled = settings.enabled;

        // 更新后端API地址配置
        if (settings.backendApiUrl !== undefined) {
            this.config.backendApiUrl = settings.backendApiUrl;
            console.log('短信服务：后端API地址已更新为', settings.backendApiUrl || '默认地址');
        }

        // 验证必要配置
        const wasInitialized = this.isInitialized;
        this.isInitialized = this.validateConfig();

        // 只在状态变化或有配置变化时输出日志
        if (hasChanges || wasInitialized !== this.isInitialized) {
            if (this.isInitialized) {
                console.log('短信服务初始化成功，后端API地址:', this.config.backendApiUrl || '默认地址');
            } else {
                console.warn('短信服务初始化失败：配置不完整 (请检查账号和密码)');
            }
        }
        console.log('SmsService: initialize 方法结束时 this.config.ceic:', this.config.ceic);
        return this.isInitialized;
    }

    /**
     * 检查配置是否有变化
     * @param {Object} settings - 新的设置
     * @returns {boolean} 是否有变化
     */
    hasConfigChanges(settings) {
        const { ceic } = this.config;
        return !!(
            (settings.account && settings.account !== ceic.account) ||
            (settings.password && settings.password !== ceic.password) ||
            (settings.enabled !== undefined && settings.enabled !== this.config.settings.enabled) ||
            (settings.backendApiUrl !== undefined && settings.backendApiUrl !== this.config.backendApiUrl)
        );
    }

    /**
     * 验证配置是否完整
     * @returns {boolean} 配置是否有效
     */
    validateConfig() {
        const { ceic } = this.config;
        return !!(
            ceic.account &&
            ceic.password
        );
    }

    /**
     * 发送单个短信
     * @param {string} phoneNumber - 手机号码
     * @param {string} content - 短信内容
     * @returns {Promise<Object>} 发送结果
     */
    async sendSms(phoneNumber, content) {
        if (!this.config.settings.enabled) {
            // 只在第一次或状态变化时输出日志
            if (!this._lastLoggedEnabledState || this._lastLoggedEnabledState !== false) {
                console.log('短信服务未启用');
                this._lastLoggedEnabledState = false;
            }
            return { success: false, message: '短信服务未启用' };
        }

        if (!this.isInitialized) {
            console.error('短信服务未初始化');
            return { success: false, message: '短信服务未初始化' };
        }

        if (!this.isValidPhoneNumber(phoneNumber)) {
            console.error('无效的手机号码:', phoneNumber);
            return { success: false, message: '无效的手机号码' };
        }

        try {
            // 构建请求参数
            const params = this.buildRequestParams(phoneNumber, content);

            // 发送请求
            const result = await this.makeRequest(params);

            if (result.success) {
                console.log('短信发送成功:', phoneNumber);
                return { success: true, message: '短信发送成功', data: result.data };
            } else {
                console.error('短信发送失败:', result.message);
                return { success: false, message: result.message || '短信发送失败' };
            }
        } catch (error) {
            console.error('短信发送异常:', error);
            return { success: false, message: error.message || '短信发送异常' };
        }
    }

    /**
     * 批量发送短信
     * @param {Array} phoneNumbers - 手机号码数组
     * @param {string} content - 短信内容
     * @returns {Promise<Object>} 发送结果
     */
    async sendBatchSms(phoneNumbers, content) {
        if (!this.config.settings.enabled) {
            return { success: false, message: '短信服务未启用' };
        }

        if (!Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
            return { success: false, message: '手机号码列表为空' };
        }

        const results = [];
        const { batchInterval } = this.config.settings;

        for (let i = 0; i < phoneNumbers.length; i++) {
            const phoneNumber = phoneNumbers[i];

            try {
                const result = await this.sendSms(phoneNumber, content);
                results.push({ phoneNumber, ...result });

                // 批量发送间隔
                if (i < phoneNumbers.length - 1 && batchInterval > 0) {
                    await this.delay(batchInterval);
                }
            } catch (error) {
                results.push({
                    phoneNumber,
                    success: false,
                    message: error.message
                });
            }
        }

        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        return {
            success: successCount > 0,
            message: `批量发送完成：成功 ${successCount} 条，失败 ${failCount} 条`,
            results,
            summary: { total: results.length, success: successCount, fail: failCount }
        };
    }

    /**
     * 构建请求参数
     * @param {string} phoneNumber - 手机号码
     * @param {string} content - 短信内容
     * @returns {Object} 请求参数
     */
    buildRequestParams(phoneNumber, content) {
        const { ceic } = this.config;

        return {
            phoneNumber: phoneNumber,
            content: content,
            account: ceic.account,
            password: ceic.password
        };
    }

    /**
     * 发送HTTP请求到后端API
     * @param {Object} params - 请求参数
     * @returns {Promise<Object>} 响应结果
     */
    async makeRequest(params) {
        const backendApiUrl = this.config.backendApiUrl || window.location.origin;

        try {
            const response = await fetch(`${backendApiUrl}/api/sms/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    phoneNumber: params.phoneNumber,
                    content: params.content,
                    account: params.account,
                    password: params.password
                })
            });

            if (response.ok) {
                const result = await response.json();
                return result; // 后端直接返回 { success: boolean, message: string, data: object }
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('后端API调用失败:', error.message);
            // 如果后端API不可用，使用模拟模式
            return this.mockApiCall(params);
        }
    }

    /**
     * 模拟API调用（用于开发和测试）
     * @param {Object} params - 请求参数
     * @returns {Promise<Object>} 模拟响应结果
     */
    async mockApiCall(params) {
        console.warn('使用模拟短信发送模式');

        // 打印输入参数
        console.log('模拟模式 - 输入参数:', {
            phoneNumber: params.phoneNumber,
            content: params.content,
            account: params.account,
            password: params.password
        });

        // 模拟成功响应
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    success: true,
                    message: '模拟短信发送成功',
                    data: { smsId: 'mock-' + Date.now(), responseCode: '1' }
                });
            }, 1000);
        });
    }

    /**
     * 验证手机号码格式
     * @param {string} phoneNumber - 手机号码
     * @returns {boolean} 是否有效
     */
    isValidPhoneNumber(phoneNumber) {
        // 简单的手机号码验证，可以根据实际需求调整
        const regex = /^1[3-9]\d{9}$/;
        return regex.test(phoneNumber);
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise<void>} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取短信服务当前状态
     * @returns {Object} 包含isEnabled和isInitialized状态的对象
     */
    getStatus() {
        return {
            isEnabled: this.config.settings.enabled,
            isInitialized: this.isInitialized
        };
    }

    /**
     * 测试短信发送功能
     * @param {string} phoneNumber - 测试手机号码
     * @returns {Promise<Object>} 测试结果
     */
    async testSms(phoneNumber) {
        const testContent = "这是一条测试短信，请勿回复。";
        console.log(`正在测试向 ${phoneNumber} 发送短信...`);
        const result = await this.sendSms(phoneNumber, testContent);
        if (result.success) {
            console.log('测试短信发送成功！', result.message);
        } else {
            console.error('测试短信发送失败:', result.message);
        }
        return result;
    }
}

// 导出 SmsService 的实例，确保单例模式
const smsService = new SmsService();
// 暴露给全局，方便在 HTML 中直接调用 (如果需要)
window.smsService = smsService;
