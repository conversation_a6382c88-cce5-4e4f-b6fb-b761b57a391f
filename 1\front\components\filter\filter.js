/**
 * 过滤器组件
 */

class Filter {
    constructor() {
        this.initEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 过滤标签监听
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', () => {
                this.handleFilterClick(tag);
            });
        });
    }

    /**
     * 处理过滤器点击事件
     * @param {HTMLElement} clickedTag - 被点击的过滤标签
     */
    handleFilterClick(clickedTag) {
        // 移除所有活动状态
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.classList.remove('active');
        });

        // 设置当前标签为活动状态
        clickedTag.classList.add('active');

        // 执行过滤
        const filterType = clickedTag.dataset.filter;
        this.filterTasks(filterType);
    }

    /**
     * 过滤任务
     * @param {string} filter - 过滤类型
     */
    filterTasks(filter) {
        // 清除统计项选中状态
        if (window.statistics) {
            window.statistics.clearSelection();
        }

        let filteredTasks = [];

        switch (filter) {
            case 'completed':
                filteredTasks = window.allTasks.filter(task =>
                    (task['整改措施完成情况'] || '').includes('已完成'));
                break;
            case 'incomplete':
                filteredTasks = window.allTasks.filter(task =>
                    !(task['整改措施完成情况'] || '').includes('已完成'));
                break;
            case 'urgent':
                filteredTasks = window.allTasks.filter(task =>
                    this.isUrgent(task) && !this.isOverdue(task));
                break;
            case 'overdue':
                filteredTasks = window.allTasks.filter(task =>
                    this.isOverdue(task));
                break;
            default:
                filteredTasks = [...window.allTasks];
        }

        // 更新全局过滤任务
        window.filteredTasks = filteredTasks;

        // 更新显示
        if (window.dashboard) {
            window.dashboard.updateDashboard();
        }

        // 保存状态
        if (window.stateManager) {
            window.stateManager.saveState();
        }
    }

    /**
     * 更新过滤标签的统计数字
     */
    updateFilterTagCounts() {
        if (!window.allTasks) return;

        // 计算各类任务数量（基于allTasks，不是filteredTasks）
        const allCount = window.allTasks.length;
        const completedCount = window.allTasks.filter(task =>
            (task['整改措施完成情况'] || '').includes('已完成')).length;
        const incompleteCount = window.allTasks.filter(task =>
            !(task['整改措施完成情况'] || '').includes('已完成')).length;
        const urgentCount = window.allTasks.filter(task =>
            this.isUrgent(task) && !this.isOverdue(task)).length;
        const overdueCount = window.allTasks.filter(task =>
            this.isOverdue(task)).length;

        // 更新显示
        const allCountElement = document.getElementById('allCount');
        const completedFilterCountElement = document.getElementById('completedFilterCount');
        const incompleteFilterCountElement = document.getElementById('incompleteFilterCount');
        const urgentFilterCountElement = document.getElementById('urgentFilterCount');
        const overdueFilterCountElement = document.getElementById('overdueFilterCount');

        if (allCountElement) allCountElement.textContent = allCount;
        if (completedFilterCountElement) completedFilterCountElement.textContent = completedCount;
        if (incompleteFilterCountElement) incompleteFilterCountElement.textContent = incompleteCount;
        if (urgentFilterCountElement) urgentFilterCountElement.textContent = urgentCount;
        if (overdueFilterCountElement) overdueFilterCountElement.textContent = overdueCount;

        // 根据统计数字动态添加/移除样式类
        this.updateFilterTagStyles(completedCount, incompleteCount, urgentCount, overdueCount);
    }

    /**
     * 根据统计数字更新过滤标签样式
     */
    updateFilterTagStyles(completedCount, incompleteCount, urgentCount, overdueCount) {
        // 获取过滤标签元素
        const completedTag = document.querySelector('[data-filter="completed"]');
        const incompleteTag = document.querySelector('[data-filter="incomplete"]');
        const urgentTag = document.querySelector('[data-filter="urgent"]');
        const overdueTag = document.querySelector('[data-filter="overdue"]');

        // 根据数量添加或移除 has-data 类
        if (completedTag) {
            if (completedCount > 0) {
                completedTag.classList.add('has-data');
            } else {
                completedTag.classList.remove('has-data');
            }
        }

        if (incompleteTag) {
            if (incompleteCount > 0) {
                incompleteTag.classList.add('has-data');
            } else {
                incompleteTag.classList.remove('has-data');
            }
        }

        if (urgentTag) {
            if (urgentCount > 0) {
                urgentTag.classList.add('has-data');
            } else {
                urgentTag.classList.remove('has-data');
            }
        }

        if (overdueTag) {
            if (overdueCount > 0) {
                overdueTag.classList.add('has-data');
            } else {
                overdueTag.classList.remove('has-data');
            }
        }
    }

    /**
     * 判断任务是否紧急
     * @param {Object} task - 任务对象
     * @returns {boolean} 是否紧急
     */
    isUrgent(task) {
        if (window.configManager) {
            return window.configManager.isTaskUrgent(task);
        }

        // 回退到默认逻辑
        const planTime = task['计划完成时间'] || '';
        const status = task['整改措施完成情况'] || '';

        if (status.includes('已完成')) return false;

        // 检查备注中是否标记为快到期
        if ((task['备注'] || '').includes('快到期')) return true;

        if (planTime && planTime !== '进行中' && planTime !== '长期') {
            try {
                // 支持多种日期格式
                let planDate;
                if (planTime.includes('.')) {
                    planDate = new Date(planTime.replace(/\./g, '-'));
                } else if (planTime.includes('/')) {
                    planDate = new Date(planTime.replace(/\//g, '-'));
                } else {
                    planDate = new Date(planTime);
                }

                const today = new Date();
                today.setHours(0, 0, 0, 0);
                planDate.setHours(0, 0, 0, 0);

                const diffDays = Math.ceil((planDate - today) / (1000 * 60 * 60 * 24));

                // 15天内到期且未过期的任务标记为快到期
                return diffDays <= 15 && diffDays >= 0;
            } catch (e) {
                console.warn('日期解析失败:', planTime, e);
                return false;
            }
        }

        return false;
    }

    /**
     * 判断任务是否已过期
     * @param {Object} task - 任务对象
     * @returns {boolean} 是否已过期
     */
    isOverdue(task) {
        if (window.configManager) {
            return window.configManager.isTaskOverdue(task);
        }

        // 回退到默认逻辑
        const planTime = task['计划完成时间'] || '';
        const status = task['整改措施完成情况'] || '';

        if (status.includes('已完成')) return false;

        if (planTime && planTime !== '进行中' && planTime !== '长期') {
            try {
                let planDate;
                if (planTime.includes('.')) {
                    planDate = new Date(planTime.replace(/\./g, '-'));
                } else if (planTime.includes('/')) {
                    planDate = new Date(planTime.replace(/\//g, '-'));
                } else {
                    planDate = new Date(planTime);
                }

                const today = new Date();
                today.setHours(0, 0, 0, 0);
                planDate.setHours(0, 0, 0, 0);

                const diffDays = Math.ceil((planDate - today) / (1000 * 60 * 60 * 24));

                return diffDays < 0; // 已过期
            } catch (e) {
                return false;
            }
        }

        return false;
    }
}

// 导出过滤器实例
const filter = new Filter();
