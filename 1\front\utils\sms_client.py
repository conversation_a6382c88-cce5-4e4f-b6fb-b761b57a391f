#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
from typing import Dict, Any, List, Optional
from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
from alibabacloud_tea_util import models as util_models

class SMSClient:
    """阿里云短信服务客户端"""
    
    def __init__(self, access_key_id: str, access_key_secret: str, endpoint: str = 'dysmsapi.aliyuncs.com'):
        """
        初始化短信客户端
        
        Args:
            access_key_id: 阿里云AccessKey ID
            access_key_secret: 阿里云AccessKey Secret
            endpoint: 短信服务端点
        """
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.endpoint = endpoint
        self._client = None
    
    def _get_client(self) -> DysmsapiClient:
        """获取短信客户端实例"""
        if self._client is None:
            config = open_api_models.Config(
                access_key_id=self.access_key_id,
                access_key_secret=self.access_key_secret,
                endpoint=self.endpoint
            )
            self._client = DysmsapiClient(config)
        return self._client
    
    def send_sms(self, phone_number: str, sign_name: str, template_code: str, 
                 template_param: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发送单条短信
        
        Args:
            phone_number: 手机号
            sign_name: 短信签名
            template_code: 短信模板代码
            template_param: 模板参数
            
        Returns:
            dict: 发送结果
        """
        try:
            client = self._get_client()
            
            # 构建请求
            request = dysmsapi_models.SendSmsRequest(
                phone_numbers=phone_number,
                sign_name=sign_name,
                template_code=template_code,
                template_param=json.dumps(template_param or {}, ensure_ascii=False)
            )
            
            # 发送请求
            runtime = util_models.RuntimeOptions()
            response = client.send_sms_with_options(request, runtime)
            
            # 处理响应
            if response.body.code == 'OK':
                return {
                    'success': True,
                    'message': '短信发送成功',
                    'data': {
                        'request_id': response.body.request_id,
                        'biz_id': response.body.biz_id,
                        'phone_number': phone_number
                    }
                }
            else:
                return {
                    'success': False,
                    'message': response.body.message or '短信发送失败',
                    'code': response.body.code,
                    'data': {
                        'request_id': response.body.request_id,
                        'phone_number': phone_number
                    }
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'短信发送异常: {str(e)}',
                'data': {
                    'phone_number': phone_number
                }
            }
    
    def send_batch_sms(self, phone_numbers: List[str], sign_name: str, 
                       template_code: str, template_param: Optional[Dict[str, Any]] = None,
                       rate_limit: int = 1) -> Dict[str, Any]:
        """
        批量发送短信
        
        Args:
            phone_numbers: 手机号列表
            sign_name: 短信签名
            template_code: 短信模板代码
            template_param: 模板参数
            rate_limit: 发送间隔（秒）
            
        Returns:
            dict: 批量发送结果
        """
        results = []
        success_count = 0
        fail_count = 0
        
        for i, phone_number in enumerate(phone_numbers):
            try:
                # 发送单条短信
                result = self.send_sms(phone_number, sign_name, template_code, template_param)
                results.append(result)
                
                if result['success']:
                    success_count += 1
                else:
                    fail_count += 1
                
                # 发送间隔控制（最后一条不需要等待）
                if i < len(phone_numbers) - 1 and rate_limit > 0:
                    time.sleep(rate_limit)
                    
            except Exception as e:
                result = {
                    'success': False,
                    'message': f'发送异常: {str(e)}',
                    'data': {
                        'phone_number': phone_number
                    }
                }
                results.append(result)
                fail_count += 1
        
        return {
            'success': success_count > 0,
            'message': f'批量发送完成：成功 {success_count} 条，失败 {fail_count} 条',
            'results': results,
            'summary': {
                'total': len(phone_numbers),
                'success': success_count,
                'fail': fail_count
            }
        }
    
    def query_send_details(self, phone_number: str, biz_id: str, 
                          send_date: Optional[str] = None) -> Dict[str, Any]:
        """
        查询短信发送详情
        
        Args:
            phone_number: 手机号
            biz_id: 发送回执ID
            send_date: 发送日期（格式：YYYYMMDD）
            
        Returns:
            dict: 查询结果
        """
        try:
            client = self._get_client()
            
            # 如果没有提供发送日期，使用今天
            if not send_date:
                send_date = time.strftime('%Y%m%d')
            
            # 构建请求
            request = dysmsapi_models.QuerySendDetailsRequest(
                phone_number=phone_number,
                biz_id=biz_id,
                send_date=send_date,
                page_size=10,
                current_page=1
            )
            
            # 发送请求
            runtime = util_models.RuntimeOptions()
            response = client.query_send_details_with_options(request, runtime)
            
            return {
                'success': True,
                'data': {
                    'code': response.body.code,
                    'message': response.body.message,
                    'request_id': response.body.request_id,
                    'total_count': response.body.total_count,
                    'sms_send_detail_dtos': response.body.sms_send_detail_dtos.sms_send_detail_dto if response.body.sms_send_detail_dtos else []
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'查询异常: {str(e)}'
            }
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试连接
        
        Returns:
            dict: 测试结果
        """
        try:
            client = self._get_client()
            
            # 尝试查询账户余额（如果有权限）或发送一个测试请求
            request = dysmsapi_models.QuerySendDetailsRequest(
                phone_number='13800000000',  # 测试号码
                send_date=time.strftime('%Y%m%d'),
                page_size=1,
                current_page=1
            )
            
            runtime = util_models.RuntimeOptions()
            response = client.query_send_details_with_options(request, runtime)
            
            return {
                'success': True,
                'message': '连接测试成功',
                'data': {
                    'request_id': response.body.request_id
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'连接测试失败: {str(e)}'
            }
