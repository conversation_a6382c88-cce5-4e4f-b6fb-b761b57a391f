/**
 * 弹窗组件
 */

class Modal {
    constructor() {
        this.detailModal = document.getElementById('detailModal');
        this.reminderModal = document.getElementById('reminderModal');
        this.reminderModalTitle = document.getElementById('reminderModalTitle');
        this.reminderContent = document.getElementById('reminderContent');
        this.sendReminderSmsBtn = document.getElementById('sendReminderSmsBtn');
        this.currentSendCallback = null; // 用于存储发送短信的回调函数

        this.initEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // ESC键关闭所有弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDetailModal();
                this.closeReminderModal();
            }
        });

        // 点击详情弹窗背景关闭
        if (this.detailModal) {
            this.detailModal.addEventListener('click', (e) => {
                if (e.target === this.detailModal) {
                    this.closeDetailModal();
                }
            });
        }

        // 点击提醒弹窗背景关闭
        if (this.reminderModal) {
            this.reminderModal.addEventListener('click', (e) => {
                if (e.target === this.reminderModal) {
                    this.closeReminderModal();
                }
            });

            // 提醒弹窗的发送按钮事件
            if (this.sendReminderSmsBtn) {
                this.sendReminderSmsBtn.addEventListener('click', async () => {
                    if (this.currentSendCallback) {
                        const content = this.reminderContent.value;
                        await this.currentSendCallback(content);
                        this.closeReminderModal();
                    }
                });
            }
        }
    }

    /**
     * 显示任务详情弹窗
     * @param {Object} task - 任务对象
     */
    showTaskDetail(task) {
        if (!this.detailModal) return;

        // 设置弹窗标题
        const titleElement = document.getElementById('detailModalTitle');
        if (titleElement) {
            titleElement.textContent = task['问题概述'] || '任务详情';
        }

        // 设置已完成工作及取得的成效
        const completedWorkElement = document.getElementById('completedWork');
        if (completedWorkElement) {
            const completedWork = task['已完成工作及取得的成效'] || task['整改措施完成情况'] || '';
            if (completedWork && completedWork !== '未提供' && completedWork.trim() !== '') {
                completedWorkElement.textContent = completedWork;
                completedWorkElement.classList.remove('empty');
            } else {
                completedWorkElement.textContent = '暂无相关信息';
                completedWorkElement.classList.add('empty');
            }
        }

        // 设置下一步工作计划
        const nextPlanElement = document.getElementById('nextPlan');
        if (nextPlanElement) {
            const nextPlan = task['下一步工作计划'] || '';
            if (nextPlan && nextPlan !== '未提供' && nextPlan.trim() !== '') {
                nextPlanElement.textContent = nextPlan;
                nextPlanElement.classList.remove('empty');
            } else {
                nextPlanElement.textContent = '暂无相关信息';
                nextPlanElement.classList.add('empty');
            }
        }

        // 显示弹窗
        this.detailModal.classList.add('show');
    }

    /**
     * 关闭详情弹窗
     */
    closeDetailModal() {
        if (this.detailModal) {
            this.detailModal.classList.remove('show');
        }
    }

    /**
     * 显示提醒弹窗
     * @param {string} title - 弹窗标题
     * @param {string} initialContent - 初始内容
     * @param {Function} onSendCallback - 点击发送时执行的回调函数，接收编辑后的内容作为参数
     */
    showReminderModal(title, initialContent, onSendCallback) {
        if (!this.reminderModal) return;

        if (this.reminderModalTitle) {
            this.reminderModalTitle.textContent = title;
        }
        if (this.reminderContent) {
            this.reminderContent.value = initialContent;
        }
        this.currentSendCallback = onSendCallback;

        this.reminderModal.classList.add('show');
    }

    /**
     * 关闭提醒弹窗
     */
    closeReminderModal() {
        if (this.reminderModal) {
            this.reminderModal.classList.remove('show');
            this.currentSendCallback = null; // 清除回调函数
        }
    }

    /**
     * 在新页面中显示任务详情
     * @param {Object} task - 任务对象
     */
    showTaskDetailInNewPage(task) {
        // 将任务数据存储到localStorage
        localStorage.setItem('currentTaskDetail', JSON.stringify(task));
        
        // 打开新页面
        window.open('task_detail.html', '_blank');
    }
}

// 导出弹窗实例
const modal = new Modal();

// 全局函数，供HTML调用
window.closeDetailModal = function() {
    modal.closeDetailModal();
};

window.closeReminderModal = function() {
    modal.closeReminderModal();
};
