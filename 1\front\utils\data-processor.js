/**
 * 数据处理工具
 */

class DataProcessor {
    constructor() {
        this.isLoading = false;
    }

    /**
     * 加载示例数据
     */
    async loadSampleData() {
        if (this.isLoading) {
            console.log('数据正在加载中，跳过重复调用');
            return;
        }

        this.isLoading = true;
        console.log('开始加载示例数据...');

        try {
            // 尝试加载JSON数据文件
            const response = await fetch('dashboard_data.json');
            if (response.ok) {
                const data = await response.json();
                window.allTasks = data.data || [];
                console.log(`从JSON文件加载了 ${window.allTasks.length} 条记录`);
            } else {
                throw new Error('JSON文件不存在，使用内置示例数据');
            }
        } catch (error) {
            console.log('加载JSON文件失败，使用内置示例数据:', error.message);
            // 内置示例数据
            window.allTasks = [
                {
                    '序号': '1',
                    '问题类别': '制度建设',
                    '问题概述': '学习贯彻习近平新时代中国特色社会主义思想不够深入',
                    '具体问题': '部分党委委员未开展党的二十大精神学习',
                    '整改措施': '组织学习党委委员"一岗双责"制度规定',
                    '整改类型': '限时整改',
                    '计划完成时间': '2023.10.30',
                    '责任领导': '党委书记',
                    '牵头整改部门': '党委办公室',
                    '整改措施完成情况': '已完成',
                    '下一步工作计划': '持续加强学习',
                    '备注': ''
                },
                {
                    '序号': '2',
                    '问题类别': '项目管理',
                    '问题概述': '绿色低碳转型发展不够',
                    '具体问题': '新能源项目投资进度缓慢',
                    '整改措施': '加快推进新能源项目建设',
                    '整改类型': '长期督办',
                    '计划完成时间': '进行中',
                    '责任领导': '总经理',
                    '牵头整改部门': '发展部',
                    '整改措施完成情况': '未完成',
                    '下一步工作计划': '制定详细实施方案',
                    '备注': '快到期'
                },
                {
                    '序号': '3',
                    '问题类别': '安全管理',
                    '问题概述': '安全生产责任制落实不到位',
                    '具体问题': '部分岗位安全操作规程执行不严格',
                    '整改措施': '完善安全管理制度，加强培训',
                    '整改类型': '限时整改',
                    '计划完成时间': '2023.12.15',
                    '责任领导': '安全总监',
                    '牵头整改部门': '安全部',
                    '整改措施完成情况': '进行中',
                    '下一步工作计划': '开展专项检查',
                    '备注': '快到期'
                }
            ];
        }

        // 过滤掉内容不完整的记录
        window.allTasks = this.filterValidTasks(window.allTasks);
        console.log(`过滤后剩余 ${window.allTasks.length} 条有效记录`);

        window.filteredTasks = [...window.allTasks];

        // 更新仪表板
        if (window.dashboard) {
            window.dashboard.updateDashboard();
        }

        // 保存状态
        if (window.stateManager) {
            window.stateManager.saveState();
        }

        this.isLoading = false;
        console.log('示例数据加载完成');
    }

    /**
     * 过滤有效任务（过滤掉超过4个"未提供"的记录）
     * @param {Array} tasks - 任务数组
     * @returns {Array} 过滤后的任务数组
     */
    filterValidTasks(tasks) {
        return tasks.filter(task => {
            const fields = [
                '问题概述', '具体问题', '整改措施', '整改类型',
                '计划完成时间', '责任领导', '牵头整改部门', '整改措施完成情况'
            ];

            let emptyCount = 0;
            fields.forEach(field => {
                const value = task[field] || '';
                if (value.trim() === '' || value.includes('未提供')) {
                    emptyCount++;
                }
            });

            // 如果超过4个字段为空或"未提供"，则过滤掉
            return emptyCount <= 4;
        });
    }

    /**
     * 解析CSV数据
     * @param {string} csvText - CSV文本
     */
    parseCSVData(csvText) {
        try {
            const lines = csvText.split('\n');
            const headers = lines[3].split(','); // 假设第4行是表头

            window.allTasks = [];
            for (let i = 4; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = lines[i].split(',');
                    const task = {};
                    headers.forEach((header, index) => {
                        task[header.trim()] = values[index] ? values[index].trim() : '';
                    });
                    window.allTasks.push(task);
                }
            }

            // 过滤掉内容不完整的记录
            window.allTasks = this.filterValidTasks(window.allTasks);
            console.log(`CSV解析完成，过滤后剩余 ${window.allTasks.length} 条有效记录`);

            window.filteredTasks = [...window.allTasks];

            if (window.dashboard) {
                window.dashboard.updateDashboard();
            }

            // 保存状态
            if (window.stateManager) {
                window.stateManager.saveState();
            }

            this.isLoading = false;
        } catch (error) {
            console.error('CSV解析错误:', error);
            this.isLoading = false;
            throw error;
        }
    }

    /**
     * 解析Excel数据
     * @param {ArrayBuffer} arrayBuffer - Excel文件的ArrayBuffer
     */
    parseExcelData(arrayBuffer) {
        try {
            const workbook = XLSX.read(arrayBuffer, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];

            // 从第4行开始读取数据
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1,
                range: 3,
                raw: false,
                defval: ''
            });

            if (jsonData.length === 0) {
                this.isLoading = false;
                alert('Excel文件中没有找到数据');
                return;
            }

            const headers = jsonData[0];
            window.allTasks = [];

            for (let i = 1; i < jsonData.length; i++) {
                const task = {};
                headers.forEach((header, index) => {
                    task[header] = jsonData[i][index] || '';
                });
                window.allTasks.push(task);
            }

            // 过滤掉内容不完整的记录
            window.allTasks = this.filterValidTasks(window.allTasks);
            console.log(`Excel解析完成，过滤后剩余 ${window.allTasks.length} 条有效记录`);

            window.filteredTasks = [...window.allTasks];

            if (window.dashboard) {
                window.dashboard.updateDashboard();
            }

            // 保存状态
            if (window.stateManager) {
                window.stateManager.saveState();
            }

            this.isLoading = false;
        } catch (error) {
            console.error('Excel解析错误:', error);
            this.isLoading = false;
            throw error;
        }
    }
}

// 导出数据处理器实例
const dataProcessor = new DataProcessor();
