/* 详情弹窗样式 */
.detail-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.detail-modal.show {
    opacity: 1;
    visibility: visible;
}

.detail-modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    transform: translateY(-50px) scale(0.9);
    transition: transform 0.3s ease;
}

.detail-modal.show .detail-modal-content {
    transform: translateY(0) scale(1);
}

.detail-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.detail-modal-title {
    font-size: 1.3em;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.detail-close-btn {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.detail-close-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section-title {
    font-size: 1.1em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
    padding-left: 10px;
    border-left: 4px solid #667eea;
}

.detail-section-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    line-height: 1.6;
    color: #333;
    min-height: 60px;
}

.detail-section-content.empty {
    color: #999;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .detail-modal-content {
        width: 95%;
        padding: 20px;
        max-height: 90vh;
    }
    
    .detail-modal-title {
        font-size: 1.1em;
    }
}

/* 提醒弹窗样式 */
.reminder-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.reminder-modal.show {
    opacity: 1;
    visibility: visible;
}

.reminder-modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px; /* 提醒弹窗可以稍微窄一些 */
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    transform: translateY(-50px) scale(0.9);
    transition: transform 0.3s ease;
}

.reminder-modal.show .reminder-modal-content {
    transform: translateY(0) scale(1);
}

.reminder-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.reminder-modal-title {
    font-size: 1.3em;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.reminder-close-btn {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.reminder-close-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.reminder-modal-body {
    margin-bottom: 20px;
}

.reminder-textarea {
    width: 100%;
    min-height: 200px; /* 设置一个合适的最小高度 */
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    resize: vertical; /* 允许垂直方向调整大小 */
    font-size: 1em;
    line-height: 1.5;
    box-sizing: border-box; /* 确保内边距和边框包含在宽度内 */
}

.reminder-modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.reminder-modal-footer button {
    margin-left: 10px;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.reminder-modal-footer .send-btn {
    background-color: #667eea; /* Primary color */
    color: white;
    border: none;
}

.reminder-modal-footer .send-btn:hover {
    background-color: #556ee0;
}

.reminder-modal-footer .cancel-btn {
    background-color: #e9ecef;
    color: #333;
    border: 1px solid #ccc;
}

.reminder-modal-footer .cancel-btn:hover {
    background-color: #dcdfe4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .reminder-modal-content {
        width: 95%;
        padding: 20px;
        max-height: 90vh;
    }
    
    .reminder-modal-title {
        font-size: 1.1em;
    }
}
