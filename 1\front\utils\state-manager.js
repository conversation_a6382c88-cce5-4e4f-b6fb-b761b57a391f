/**
 * 状态管理器 - 处理跨页面的数据持久化
 */

class StateManager {
    constructor() {
        this.storageKey = 'dashboardState';
        this.sessionKey = 'dashboardSession';
        this.autoSaveInterval = null;
        this.lastSaveTime = 0;
        this.saveThrottle = 1000; // 1秒内最多保存一次
    }

    /**
     * 初始化状态管理器
     */
    init() {
        // 页面加载时恢复状态
        this.restoreState();

        // 设置自动保存
        this.startAutoSave();

        // 页面卸载时保存状态
        window.addEventListener('beforeunload', () => {
            this.saveState();
        });

        // 页面可见性变化时保存状态
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveState();
            }
        });

        console.log('状态管理器初始化完成');
    }

    /**
     * 保存当前状态
     */
    saveState() {
        const now = Date.now();
        if (now - this.lastSaveTime < this.saveThrottle) {
            return; // 节流，避免频繁保存
        }

        try {
            const state = this.collectCurrentState();

            // 保存到 localStorage（持久化）
            localStorage.setItem(this.storageKey, JSON.stringify(state));

            // 保存到 sessionStorage（会话级别）
            sessionStorage.setItem(this.sessionKey, JSON.stringify({
                ...state,
                timestamp: now
            }));

            this.lastSaveTime = now;
            console.log('状态已保存', state);
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    /**
     * 恢复状态
     */
    restoreState() {
        try {
            // 优先从 sessionStorage 恢复（更新）
            let state = null;
            const sessionData = sessionStorage.getItem(this.sessionKey);

            if (sessionData) {
                const sessionState = JSON.parse(sessionData);
                // 检查会话数据是否在合理时间内（1小时）
                if (Date.now() - sessionState.timestamp < 3600000) {
                    state = sessionState;
                    console.log('从会话存储恢复状态');
                }
            }

            // 如果会话数据无效，从 localStorage 恢复
            if (!state) {
                const savedData = localStorage.getItem(this.storageKey);
                if (savedData) {
                    state = JSON.parse(savedData);
                    console.log('从本地存储恢复状态');
                }
            }

            if (state) {
                this.applyState(state);
                this.showNotification('数据状态已恢复', 'success');
                return true;
            }
        } catch (error) {
            console.error('恢复状态失败:', error);
            this.showNotification('状态恢复失败', 'error');
        }
        return false;
    }

    /**
     * 收集当前状态
     */
    collectCurrentState() {
        const state = {
            // 数据状态
            allTasks: window.allTasks || [],
            filteredTasks: window.filteredTasks || [],

            // 过滤状态
            currentFilter: this.getCurrentFilter(),
            selectedStatFilter: window.statistics?.selectedStatFilter || null,

            // 文件状态
            fileInfo: this.getFileInfo(),

            // UI状态
            uiState: {
                isDataLoaded: !!(window.allTasks && window.allTasks.length > 0),
                totalCount: window.filteredTasks?.length || 0
            },

            // 时间戳
            timestamp: Date.now()
        };

        return state;
    }

    /**
     * 应用状态
     */
    applyState(state) {
        if (!state) return;

        try {
            // 恢复数据
            if (state.allTasks && state.allTasks.length > 0) {
                window.allTasks = state.allTasks;
                window.filteredTasks = state.filteredTasks || state.allTasks;

                console.log(`恢复了 ${window.allTasks.length} 条任务数据`);

                // 恢复过滤状态
                if (state.currentFilter) {
                    this.applyFilter(state.currentFilter);
                }

                // 恢复统计过滤状态
                if (state.selectedStatFilter && window.statistics) {
                    window.statistics.selectedStatFilter = state.selectedStatFilter;
                }

                // 恢复文件信息显示
                if (state.fileInfo) {
                    this.restoreFileInfo(state.fileInfo);
                }

                // 更新界面
                this.updateUI();

                // 标记数据已加载
                this.markDataAsLoaded();
            }
        } catch (error) {
            console.error('应用状态失败:', error);
        }
    }

    /**
     * 获取当前过滤状态
     */
    getCurrentFilter() {
        const activeFilter = document.querySelector('.filter-tag.active');
        return activeFilter ? activeFilter.dataset.filter : 'all';
    }

    /**
     * 应用过滤器
     */
    applyFilter(filterType) {
        // 更新过滤标签状态
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.classList.remove('active');
            if (tag.dataset.filter === filterType) {
                tag.classList.add('active');
            }
        });

        // 应用过滤逻辑
        if (window.filter) {
            window.filter.filterTasks(filterType);
        }
    }

    /**
     * 获取文件信息
     */
    getFileInfo() {
        const fileInput = document.getElementById('fileInput');
        if (fileInput && fileInput.files.length > 0) {
            const file = fileInput.files[0];
            return {
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified
            };
        }
        return null;
    }

    /**
     * 恢复文件信息显示
     */
    restoreFileInfo(fileInfo) {
        if (fileInfo) {
            // 可以在界面上显示已加载的文件信息
            console.log(`已恢复文件: ${fileInfo.name}`);

            // 如果需要，可以在界面上添加文件状态指示
            this.showFileStatus(fileInfo);
        }
    }

    /**
     * 显示文件状态
     */
    showFileStatus(fileInfo) {
        // 在控制面板中显示文件状态
        const controlGroup = document.querySelector('.control-group');
        if (controlGroup) {
            let statusElement = document.getElementById('fileStatus');
            if (!statusElement) {
                statusElement = document.createElement('div');
                statusElement.id = 'fileStatus';
                statusElement.className = 'file-status';
                controlGroup.appendChild(statusElement);
            }

            statusElement.innerHTML = `
                <span class="file-status-icon">📄</span>
                <span class="file-status-text">已加载: ${fileInfo.name}</span>
                <button class="file-status-clear" onclick="stateManager.clearFileStatus()">×</button>
            `;
        }
    }

    /**
     * 清除文件状态
     */
    clearFileStatus() {
        const statusElement = document.getElementById('fileStatus');
        if (statusElement) {
            statusElement.remove();
        }

        // 清除文件输入
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.value = '';
        }

        // 清除数据
        window.allTasks = [];
        window.filteredTasks = [];

        // 更新界面
        this.updateUI();

        // 清除保存的状态
        this.clearState();
    }

    /**
     * 更新界面
     */
    updateUI() {
        if (window.dashboard) {
            window.dashboard.updateDashboard();
        }
    }

    /**
     * 标记数据已加载
     */
    markDataAsLoaded() {
        // 移除加载提示
        const loadingElement = document.querySelector('.loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }

    /**
     * 开始自动保存
     */
    startAutoSave() {
        // 每30秒自动保存一次
        this.autoSaveInterval = setInterval(() => {
            if (window.allTasks && window.allTasks.length > 0) {
                this.saveState();
            }
        }, 30000);
    }

    /**
     * 停止自动保存
     */
    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }

    /**
     * 清除状态
     */
    clearState() {
        try {
            localStorage.removeItem(this.storageKey);
            sessionStorage.removeItem(this.sessionKey);
            console.log('状态已清除');
        } catch (error) {
            console.error('清除状态失败:', error);
        }
    }

    /**
     * 获取状态信息
     */
    getStateInfo() {
        const localStorage_data = localStorage.getItem(this.storageKey);
        const sessionStorage_data = sessionStorage.getItem(this.sessionKey);

        return {
            hasLocalStorage: !!localStorage_data,
            hasSessionStorage: !!sessionStorage_data,
            localStorageSize: localStorage_data ? localStorage_data.length : 0,
            sessionStorageSize: sessionStorage_data ? sessionStorage_data.length : 0
        };
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `state-restore-notification ${type}`;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 检查是否有可恢复的状态
     */
    hasRestorableState() {
        try {
            const sessionData = sessionStorage.getItem(this.sessionKey);
            const localData = localStorage.getItem(this.storageKey);

            if (sessionData) {
                const sessionState = JSON.parse(sessionData);
                if (Date.now() - sessionState.timestamp < 3600000) {
                    return sessionState.allTasks && sessionState.allTasks.length > 0;
                }
            }

            if (localData) {
                const localState = JSON.parse(localData);
                return localState.allTasks && localState.allTasks.length > 0;
            }
        } catch (error) {
            console.error('检查可恢复状态失败:', error);
        }
        return false;
    }

    /**
     * 手动触发状态恢复
     */
    manualRestore() {
        if (this.hasRestorableState()) {
            const restored = this.restoreState();
            if (restored) {
                this.showNotification('数据已成功恢复', 'success');
            } else {
                this.showNotification('数据恢复失败', 'error');
            }
        } else {
            this.showNotification('没有可恢复的数据', 'warning');
        }
    }

    /**
     * 销毁状态管理器
     */
    destroy() {
        this.stopAutoSave();
        this.saveState();
    }
}

// 创建全局状态管理器实例
const stateManager = new StateManager();

// 导出供其他模块使用
window.stateManager = stateManager;
