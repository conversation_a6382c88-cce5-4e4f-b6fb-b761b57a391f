/**
 * 任务操作工具
 */

class TaskActions {
    constructor() {
        this.settings = this.loadSettings();
    }

    /**
     * 加载设置
     * @returns {Object} 设置对象
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem('dashboardSettings');
            if (saved) {
                return { ...AppConfig, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
        return { ...AppConfig };
    }

    /**
     * 发送督办提醒
     * @param {number} taskIndex - 任务索引
     */
    async sendReminder(taskIndex) {
        if (!window.filteredTasks || !window.filteredTasks[taskIndex]) {
            alert('任务不存在');
            return;
        }

        const task = window.filteredTasks[taskIndex];
        
        let reminderText = `督办提醒：\n`;
        if (task['问题概述'] && task['问题概述'].trim() !== '') {
            reminderText += `任务：${task['问题概述']}\n`;
        } else if (task['整改措施'] && task['整改措施'].trim() !== '') {
            reminderText += `整改措施：${task['整改措施']}\n`;
        } else {
            reminderText += `任务/整改措施：未提供\n`;
        }

        reminderText += `责任领导：${task['责任领导'] || '未提供'}\n`;
        reminderText += `责任部门：${task['责任部门'] || task['牵头整改部门'] || '未提供'}\n`;
        reminderText += `计划完成时间：${task['计划完成时间'] || '未提供'}\n`;
        reminderText += `当前状态：${task['整改措施完成情况'] || '未提供'}\n\n`;
        reminderText += `请及时关注任务进展！`;

        // 显示可编辑的提醒弹窗
        window.modal.showReminderModal(
            '督办提醒',
            reminderText,
            async (editedContent) => {
                await this.sendSmsReminder(task, editedContent);
            }
        );
    }

    /**
     * 发送短信提醒
     * @param {Object} task - 任务对象
     * @param {string} content - 短信内容
     */
    async sendSmsReminder(task, content) {
        // 重新加载最新设置
        this.settings = this.loadSettings();

        if (!this.settings.sms?.settings?.enabled) {
            console.log('短信服务未启用');
            return;
        }

        if (!window.smsService) {
            console.error('短信服务未初始化');
            return;
        }

        // 初始化短信服务
        window.smsService.initialize(this.settings.sms.ceic);

        // 构建短信内容
        // const messageContent = `督办提醒：\n任务：${task['问题概述'] || '未提供'}\n负责人：${task['责任领导'] || '未提供'}\n部门：${task['牵头整改部门'] || '未提供'}\n计划完成时间：${task['计划完成时间'] || '未提供'}\n当前状态：${task['整改措施完成情况'] || '未提供'}\n\n请及时关注任务进展！`;

        // 这里需要获取负责人的手机号
        const phoneNumber = this.getPhoneNumber(task);

        if (phoneNumber) {
            try {
                const result = await window.smsService.sendSms(phoneNumber, content);
                if (result.success) {
                    console.log('短信提醒发送成功');
                } else {
                    console.error('短信提醒发送失败:', result.message);
                }
            } catch (error) {
                console.error('短信发送异常:', error);
            }
        } else {
            console.warn('未找到负责人手机号，跳过短信提醒');
        }
    }

    /**
     * 获取负责人手机号
     * @param {Object} task - 任务对象
     * @returns {string|null} 手机号或null
     */
    getPhoneNumber(task) {
        // 优先从任务数据中的手机号字段获取
        if (task['联系电话'] || task['手机号'] || task['电话']) {
            return task['联系电话'] || task['手机号'] || task['电话'];
        }

        // 从联系人映射表中查找
        const contactMap = this.getContactMap();

        // 根据配置的联系人类型决定查找方式
        const contactType = this.settings.contacts?.settings?.contactType || 'department';

        if (contactType === 'department') {
            // 优先根据牵头部门查找（推荐方式）
            const department = task['牵头整改部门'] || '';
            if (department && contactMap[department]) {
                return contactMap[department];
            }
        }

        // 备用方案：根据责任领导查找
        const responsible = task['责任领导'] || '';
        if (responsible && contactMap[responsible]) {
            return contactMap[responsible];
        }

        return null;
    }

    /**
     * 获取联系人映射表
     * @returns {Object} 联系人映射表
     */
    getContactMap() {
        // 从设置中获取联系人映射表
        const contactMapping = this.settings.contacts?.mapping || {};

        // 如果设置中没有配置，返回空对象
        if (!contactMapping || Object.keys(contactMapping).length === 0) {
            console.warn('联系人映射表未配置，请在设置页面中配置联系人信息');
            return {};
        }

        // 过滤掉空的手机号
        const validContacts = {};
        for (const [name, phone] of Object.entries(contactMapping)) {
            if (phone && phone.trim()) {
                validContacts[name] = phone.trim();
            }
        }

        return validContacts;
    }

    /**
     * 批量督办提醒
     */
    async batchReminder() {
        if (!window.filteredTasks) {
            alert('没有任务数据');
            return;
        }

        const urgentTasks = window.filteredTasks.filter(task => {
            const isCompleted = (task['整改措施完成情况'] || '').includes('已完成');
            return !isCompleted && (this.isUrgent(task) || (task['备注'] || '').includes('快到期'));
        });

        if (urgentTasks.length === 0) {
            alert('当前没有需要督办的紧急任务');
            return;
        }

        // 获取自定义短信内容
        const customContentElement = document.getElementById('customSmsContent');
        let initialContent = customContentElement ? customContentElement.value.trim() : '';

        if (initialContent === '') {
            // 如果没有自定义内容，生成默认批量提醒内容
            initialContent = `批量督办提醒：\n共有 ${urgentTasks.length} 项紧急任务需要关注：\n\n${urgentTasks.map((task, index) =>
                `${index + 1}. ${task['问题概述'] || '未提供'} (${task['牵头整改部门'] || '未提供'})`
            ).join('\n')}\n\n请相关部门及时处理！`;
        }

        // 显示可编辑的提醒弹窗
        window.modal.showReminderModal(
            '批量督办提醒',
            initialContent,
            async (editedContent) => {
                await this.sendBatchSmsReminder(urgentTasks, editedContent);
            }
        );
    }

    /**
     * 发送批量短信提醒
     * @param {Array} urgentTasks - 紧急任务列表
     * @param {string} content - 短信内容
     */
    async sendBatchSmsReminder(urgentTasks, content) {
        // 重新加载最新设置
        this.settings = this.loadSettings();

        if (!this.settings.sms?.settings?.enabled) {
            console.log('短信服务未启用');
            return;
        }

        if (!window.smsService) {
            console.error('短信服务未初始化');
            return;
        }

        // 初始化短信服务
        window.smsService.initialize(this.settings.sms.ceic);

        // 收集所有需要发送短信的手机号
        const phoneNumbers = [];
        const tasksByPhone = {};

        urgentTasks.forEach(task => {
            const phoneNumber = this.getPhoneNumber(task);
            if (phoneNumber) {
                if (!tasksByPhone[phoneNumber]) {
                    tasksByPhone[phoneNumber] = [];
                    phoneNumbers.push(phoneNumber);
                }
                tasksByPhone[phoneNumber].push(task);
            }
        });

        if (phoneNumbers.length === 0) {
            console.warn('没有找到有效的手机号，跳过批量短信提醒');
            return;
        }

        console.log(`准备向 ${phoneNumbers.length} 个手机号发送批量督办提醒`);

        // 为每个手机号发送个性化的短信
        for (const phoneNumber of phoneNumbers) {
            // const tasks = tasksByPhone[phoneNumber];
            // const taskListSummary = tasks.map(t => t['问题概述'] || '未提供').slice(0, 3).join('、');
            // const messageContent = `批量督办提醒：\n您有 ${tasks.length} 项紧急任务需要关注，包括：${taskListSummary}等。\n请相关部门及时处理！`;

            try {
                const result = await window.smsService.sendSms(phoneNumber, content); // 使用传入的 content
                if (result.success) {
                    console.log(`批量短信提醒发送成功: ${phoneNumber}`);
                } else {
                    console.error(`批量短信提醒发送失败: ${phoneNumber}`, result.message);
                }
            } catch (error) {
                console.error(`批量短信发送异常: ${phoneNumber}`, error);
            }
        }
    }

    /**
     * 显示任务详情
     * @param {number} taskIndex - 任务索引
     * @param {boolean} useNewPage - 是否在新页面显示
     */
    showTaskDetail(taskIndex, useNewPage = false) {
        if (!window.filteredTasks || !window.filteredTasks[taskIndex]) {
            alert('任务不存在');
            return;
        }

        const task = window.filteredTasks[taskIndex];

        if (useNewPage) {
            // 在新页面中显示详情
            if (window.modal) {
                window.modal.showTaskDetailInNewPage(task);
            }
        } else {
            // 在弹窗中显示详情
            if (window.modal) {
                window.modal.showTaskDetail(task);
            }
        }
    }

    /**
     * 判断任务是否紧急
     * @param {Object} task - 任务对象
     * @returns {boolean} 是否紧急
     */
    isUrgent(task) {
        const planTime = task['计划完成时间'] || '';
        const status = task['整改措施完成情况'] || '';

        if (status.includes('已完成')) return false;

        if (planTime && planTime !== '进行中' && planTime !== '长期') {
            try {
                const planDate = new Date(planTime.replace(/\./g, '-'));
                const today = new Date();
                const diffDays = Math.ceil((planDate - today) / (1000 * 60 * 60 * 24));

                // 使用配置的紧急任务阈值
                const threshold = this.settings.time?.urgentThreshold || 7;
                return diffDays <= threshold && diffDays >= 0;
            } catch (e) {
                return false;
            }
        }

        return false;
    }
}

// 导出任务操作实例
const taskActions = new TaskActions();

// 全局函数，供HTML调用
function sendReminder(taskIndex) {
    taskActions.sendReminder(taskIndex);
}

function batchReminder() {
    taskActions.batchReminder();
}

function showTaskDetail(taskIndex, useNewPage = false) {
    taskActions.showTaskDetail(taskIndex, useNewPage);
}
