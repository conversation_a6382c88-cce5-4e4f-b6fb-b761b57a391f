/**
 * 设置页面样式
 */

/* 容器布局 */
.container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    gap: 20px;
    min-height: calc(100vh - 120px);
}

/* 头部操作按钮 */
.header-actions {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.back-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.back-btn:hover {
    background: #5a6fd8;
}

/* 设置导航 */
.settings-nav {
    width: 250px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px 0;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: #f8f9ff;
    border-left-color: #667eea;
}

.nav-item.active {
    background: #f8f9ff;
    border-left-color: #667eea;
    color: #667eea;
    font-weight: 500;
}

.nav-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* 设置内容区域 */
.settings-content {
    flex: 1;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.settings-panel {
    display: none;
    padding: 30px;
}

.settings-panel.active {
    display: block;
}

.panel-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.panel-header h2 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.panel-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* 设置组 */
.settings-group {
    margin-bottom: 30px;
}

.settings-group h3 {
    margin: 0 0 20px 0;
    color: #444;
    font-size: 18px;
    font-weight: 500;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

/* 设置项 */
.setting-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #eee;
}

.setting-item label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.setting-item input[type="text"],
.setting-item input[type="password"],
.setting-item input[type="number"],
.setting-item input[type="tel"],
.setting-item input[type="time"] {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background: white;
}

.setting-item input[type="text"]:focus,
.setting-item input[type="password"]:focus,
.setting-item input[type="number"]:focus,
.setting-item input[type="tel"]:focus,
.setting-item input[type="time"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    cursor: pointer;
}

.setting-desc {
    font-size: 12px;
    color: #666;
    margin-top: 6px;
    line-height: 1.4;
}

/* 服务状态 */
.service-status {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-top: 10px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    background: #dc3545;
}

.status-indicator.success {
    background: #28a745;
}

.status-indicator.warning {
    background: #ffc107;
}

.status-text {
    font-size: 14px;
    color: #666;
}

/* 测试按钮 */
.test-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
    transition: background-color 0.3s ease;
    align-self: flex-start;
}

.test-btn:hover {
    background: #218838;
}

.test-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* 信息项 */
.info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #666;
    width: 120px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
}

/* 功能列表 */
.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.feature-item {
    padding: 20px;
    background: #f8f9ff;
    border-radius: 8px;
    border: 1px solid #e6e9ff;
}

.feature-item h4 {
    margin: 0 0 10px 0;
    color: #667eea;
    font-size: 16px;
}

.feature-item p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/* 操作按钮 */
.settings-actions {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    background: white;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.settings-actions button {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.save-btn {
    background: #28a745;
    color: white;
}

.save-btn:hover {
    background: #218838;
}

.reset-btn {
    background: #dc3545;
    color: white;
}

.reset-btn:hover {
    background: #c82333;
}

.export-btn,
.import-btn {
    background: #667eea;
    color: white;
}

.export-btn:hover,
.import-btn:hover {
    background: #5a6fd8;
}

/* 联系人管理样式 */
.contact-mapping-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 8px;
}

.contact-mapping-header {
    display: grid;
    grid-template-columns: 1fr 1fr 100px;
    gap: 10px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.contact-mapping-list {
    max-height: 300px;
    overflow-y: auto;
}

.contact-mapping-item {
    display: grid;
    grid-template-columns: 1fr 1fr 100px;
    gap: 10px;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.contact-mapping-item:last-child {
    border-bottom: none;
}

.contact-mapping-item.invalid {
    background: #fff5f5;
    border-left: 3px solid #e74c3c;
}

.contact-name-input,
.contact-phone-input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 14px;
    width: 100%;
}

.contact-name-input:focus,
.contact-phone-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.contact-phone-input.invalid {
    border-color: #e74c3c;
    background: #fff5f5;
}

.contact-remove-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.contact-remove-btn:hover {
    background: #c0392b;
}

.contact-mapping-actions {
    padding: 16px;
    background: #f8f9fa;
    border-top: 1px solid #ddd;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.add-contact-btn,
.auto-detect-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.add-contact-btn:hover,
.auto-detect-btn:hover {
    background: #5a6fd8;
}

.auto-detect-btn {
    background: #27ae60;
}

.auto-detect-btn:hover {
    background: #229954;
}

.contact-text-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.parse-btn,
.clear-btn {
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.parse-btn:hover {
    background: #2980b9;
}

.clear-btn {
    background: #e74c3c;
}

.clear-btn:hover {
    background: #c0392b;
}

.contact-status {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    color: #666;
    font-size: 14px;
}

.status-value {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

#contactsTextArea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    min-height: 120px;
}

#contactsTextArea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        padding: 10px;
        gap: 10px;
    }

    .settings-nav {
        width: 100%;
        position: static;
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
    }

    .nav-item {
        flex-shrink: 0;
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .nav-item:hover,
    .nav-item.active {
        border-left: none;
        border-bottom-color: #667eea;
    }

    .settings-panel {
        padding: 20px;
    }

    .panel-header h2 {
        font-size: 20px;
    }

    .feature-list {
        grid-template-columns: 1fr;
    }

    .settings-actions {
        position: static;
        margin-top: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .header-actions {
        position: static;
        transform: none;
        margin-top: 10px;
    }
}
