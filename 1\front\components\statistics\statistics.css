/* 统计面板样式 */
.stats-panel {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.stats-title {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
    cursor: pointer;
    user-select: none;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-item.selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.stat-item.selected .stat-number {
    color: white !important;
}

.stat-item.selected .stat-label {
    color: rgba(255, 255, 255, 0.9);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    color: #666;
    font-weight: 500;
}

/* 分类颜色 */
.stat-item.category-制度建设 { border-left-color: #e74c3c; }
.stat-item.category-制度建设 .stat-number { color: #e74c3c; }

.stat-item.category-项目管理 { border-left-color: #3498db; }
.stat-item.category-项目管理 .stat-number { color: #3498db; }

.stat-item.category-安全管理 { border-left-color: #f39c12; }
.stat-item.category-安全管理 .stat-number { color: #f39c12; }

.stat-item.category-质量管理 { border-left-color: #9b59b6; }
.stat-item.category-质量管理 .stat-number { color: #9b59b6; }

.stat-item.category-财务管理 { border-left-color: #27ae60; }
.stat-item.category-财务管理 .stat-number { color: #27ae60; }

.stat-item.category-人事管理 { border-left-color: #e67e22; }
.stat-item.category-人事管理 .stat-number { color: #e67e22; }

.stat-item.category-其他 { border-left-color: #95a5a6; }
.stat-item.category-其他 .stat-number { color: #95a5a6; }

.stats-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin-top: 15px;
}

.stats-summary .total-count {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-summary .total-label {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 分类标题 */
.category-title {
    font-size: 1.2em;
    font-weight: bold;
    margin: 20px 0 15px 0;
    color: #333;
    padding-left: 10px;
    border-left: 4px solid #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-number {
        font-size: 1.5em;
    }
    
    .stats-summary .total-count {
        font-size: 2em;
    }
}
