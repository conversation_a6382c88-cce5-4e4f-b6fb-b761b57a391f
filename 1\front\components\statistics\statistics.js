/**
 * 统计组件
 */

class Statistics {
    constructor() {
        this.selectedStatFilter = null;
    }

    /**
     * 更新统计信息
     */
    updateStatistics() {
        if (!window.filteredTasks) return;

        // 更新总数
        const totalCountElement = document.getElementById('totalCount');
        if (totalCountElement) {
            totalCountElement.textContent = window.filteredTasks.length;
        }

        // 更新过滤标签统计
        if (window.filter) {
            window.filter.updateFilterTagCounts();
        }

        // 统计牵头部门
        this.updateDepartmentStats();

        // 统计整改类型
        this.updateTypeStats();
    }

    /**
     * 统计牵头部门
     */
    updateDepartmentStats() {
        const departmentStats = {};
        window.filteredTasks.forEach(task => {
            const department = task['牵头整改部门'] || '未指定';
            if (department !== '未提供' && department.trim() !== '') {
                departmentStats[department] = (departmentStats[department] || 0) + 1;
            }
        });

        this.renderStatsGrid('departmentStats', departmentStats, 'department');
    }

    /**
     * 统计整改类型
     */
    updateTypeStats() {
        const typeStats = {};
        window.filteredTasks.forEach(task => {
            const type = task['整改类型'] || '未分类';
            if (type !== '未提供' && type.trim() !== '') {
                typeStats[type] = (typeStats[type] || 0) + 1;
            }
        });

        this.renderStatsGrid('typeStats', typeStats, 'type');
    }

    /**
     * 渲染统计网格
     * @param {string} containerId - 容器ID
     * @param {Object} stats - 统计数据
     * @param {string} type - 统计类型
     */
    renderStatsGrid(containerId, stats, type) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';

        // 按数量排序
        const sortedStats = Object.entries(stats).sort((a, b) => b[1] - a[1]);

        if (sortedStats.length === 0) {
            container.innerHTML = '<div class="stat-item"><div class="stat-number">0</div><div class="stat-label">暂无数据</div></div>';
            return;
        }

        sortedStats.forEach(([name, count]) => {
            const statItem = document.createElement('div');
            statItem.className = `stat-item ${type}-${name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')}`;

            // 检查是否为当前选中的统计项
            if (this.selectedStatFilter && this.selectedStatFilter.type === type && this.selectedStatFilter.value === name) {
                statItem.classList.add('selected');
            }

            statItem.innerHTML = `
                <div class="stat-number">${count}</div>
                <div class="stat-label">${name}</div>
            `;

            // 添加点击事件
            statItem.addEventListener('click', () => {
                this.handleStatItemClick(type, name, statItem);
            });

            container.appendChild(statItem);
        });
    }

    /**
     * 处理统计项点击事件
     * @param {string} type - 统计类型
     * @param {string} value - 统计值
     * @param {HTMLElement} element - 点击的元素
     */
    handleStatItemClick(type, value, element) {
        // 如果点击的是已选中的项，则取消选中
        if (this.selectedStatFilter && this.selectedStatFilter.type === type && this.selectedStatFilter.value === value) {
            this.selectedStatFilter = null;
            element.classList.remove('selected');
            // 恢复显示所有任务
            this.applyCurrentFilter();
        } else {
            // 清除之前的选中状态
            this.clearSelection();

            // 设置新的选中状态
            this.selectedStatFilter = { type: type, value: value };
            element.classList.add('selected');

            // 应用统计项过滤
            this.applyStatFilter();
        }
    }

    /**
     * 清除选中状态
     */
    clearSelection() {
        this.selectedStatFilter = null;
        document.querySelectorAll('.stat-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
    }

    /**
     * 应用统计项过滤
     */
    applyStatFilter() {
        if (!this.selectedStatFilter) {
            this.applyCurrentFilter();
            return;
        }

        const fieldMap = {
            'department': '牵头整改部门',
            'type': '整改类型'
        };

        const fieldName = fieldMap[this.selectedStatFilter.type];
        if (!fieldName) return;

        window.filteredTasks = window.allTasks.filter(task => {
            const fieldValue = task[fieldName] || '';
            return fieldValue === this.selectedStatFilter.value;
        });

        // 更新显示
        if (window.taskCard) {
            window.taskCard.renderTasks(window.filteredTasks);
        }
        this.updateStatistics();

        // 保存状态
        if (window.stateManager) {
            window.stateManager.saveState();
        }
    }

    /**
     * 应用当前过滤器（不包括统计项过滤）
     */
    applyCurrentFilter() {
        const activeFilter = document.querySelector('.filter-tag.active');
        const filterType = activeFilter ? activeFilter.dataset.filter : 'all';

        if (window.filter) {
            window.filter.filterTasks(filterType);
        }
    }
}

// 导出统计实例
const statistics = new Statistics();
