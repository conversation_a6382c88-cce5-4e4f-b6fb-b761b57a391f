/**
 * 应用配置文件
 */

const AppConfig = {
    // 应用信息
    app: {
        name: '整改工作台账督办仪表板',
        version: '2.0.0',
        description: '多维度数据分析与督办提醒系统'
    },

    // 数据配置
    data: {
        // 必填字段
        requiredFields: [
            '问题概述', '具体问题', '整改措施', '整改类型',
            '计划完成时间', '责任领导', '牵头整改部门', '整改措施完成情况'
        ],

        // 最大允许空字段数量
        maxEmptyFields: 4,

        // 默认数据文件
        defaultDataFile: 'dashboard_data.json',

        // 支持的文件格式
        supportedFormats: ['.csv', '.xlsx', '.xls', '.json']
    },

    // UI配置
    ui: {
        // 任务卡片
        taskCard: {
            minHeight: 300,
            mobileMinHeight: 280,
            gridColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
            mobileGridColumns: '1fr'
        },

        // 统计面板
        statistics: {
            gridColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            mobileGridColumns: 'repeat(2, 1fr)'
        },

        // 动画配置
        animation: {
            duration: '0.3s',
            easing: 'ease'
        }
    },

    // 颜色主题
    colors: {
        primary: '#667eea',
        secondary: '#764ba2',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        info: '#3498db',

        // 状态颜色
        completed: '#27ae60',
        incomplete: '#e74c3c',
        urgent: '#f39c12',

        // 分类颜色
        categories: {
            '制度建设': '#e74c3c',
            '项目管理': '#3498db',
            '安全管理': '#f39c12',
            '质量管理': '#9b59b6',
            '财务管理': '#27ae60',
            '人事管理': '#e67e22',
            '其他': '#95a5a6'
        }
    },

    // 时间配置
    time: {
        // 紧急任务阈值（天）
        urgentThreshold: 7,

        // 日期格式
        dateFormat: 'YYYY.MM.DD',

        // 时间显示格式
        timeFormat: 'YYYY-MM-DD HH:mm:ss'
    },

    // 联系人配置
    contacts: {
        // 联系人映射表（牵头部门 -> 负责人手机号）
        mapping: {
            // 示例配置，请根据实际情况修改
            '党委办公室': '',
            '发展部': '',
            '安全部': '',
            '财务部': '',
            '人力资源部': '',
            '质量部': '',
            '信息技术部': '',
            '环保部': ''
        },

        // 联系人配置选项
        settings: {
            // 是否启用联系人验证
            enableValidation: true,

            // 手机号格式验证
            phoneValidation: true,

            // 自动从数据中提取联系人
            autoExtractFromData: true,

            // 联系人类型：'department' 或 'responsible'
            contactType: 'department'
        }
    },

    // 短信服务配置
    sms: {
        // CEIC 短信服务配置
        ceic: {
            // 平台账号（从环境变量或设置中获取）
            account: '', // 示例值，请替换为您的实际账号
            // 平台密码（从环境变量或设置中获取，注意这里是明文，后端会MD5加密）
            password: '' // 示例值，请替换为您的实际密码
        },

        // 短信发送配置
        settings: {
            // 是否启用短信提醒
            enabled: true, // 默认启用
            // 批量发送间隔（毫秒）
            batchInterval: 1000,
            // 最大重试次数
            maxRetries: 3,
            // 超时时间（毫秒）
            timeout: 10000
        },

        // 后端API配置
        backendApiUrl: 'http://localhost:5000', // 假设后端服务运行在5000端口

        // 短信模板参数 (此部分可能不再需要，但保留以防万一)
        templates: {
            // 单个任务提醒模板
            singleTask: {
                code: '',
                params: ['taskName', 'responsible', 'department', 'deadline']
            },

            // 批量任务提醒模板
            batchTasks: {
                code: '',
                params: ['taskCount', 'department']
            }
        }
    },

    // 文件配置
    file: {
        // CSV解析配置
        csv: {
            headerRow: 3, // 表头行号（从0开始）
            dataStartRow: 4, // 数据开始行号
            encoding: 'utf-8'
        },

        // Excel解析配置
        excel: {
            headerRow: 3,
            dataStartRow: 4,
            sheetIndex: 0
        },

        // 导出配置
        export: {
            format: 'json',
            filename: '督办报告_{date}.json'
        }
    },

    // 消息配置
    messages: {
        loading: '正在加载数据...',
        noData: '暂无数据',
        fileError: '文件解析失败，请检查文件格式',
        noFile: '请先选择文件',
        loadingInProgress: '数据正在加载中，请稍候...',
        noUrgentTasks: '当前没有需要督办的紧急任务',
        taskNotFound: '任务不存在',
        noDataToExport: '没有数据可导出'
    },

    // 功能开关
    features: {
        enableBatchReminder: true,
        enableExport: true,
        enableNewPageDetail: true,
        enableStatisticsFilter: true,
        enableAutoSave: false
    },

    // 调试配置
    debug: {
        enabled: false,
        logLevel: 'info', // 'debug', 'info', 'warn', 'error'
        showPerformance: false
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppConfig;
} else {
    window.AppConfig = AppConfig;
}

console.log('AppConfig: 初始加载 AppConfig.sms.ceic:', AppConfig.sms.ceic);
