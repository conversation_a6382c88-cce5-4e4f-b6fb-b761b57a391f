/**
 * 仪表板主控制器
 */

class Dashboard {
    constructor() {
        // 全局变量
        window.allTasks = [];
        window.filteredTasks = [];

        // 组件实例
        window.taskCard = taskCard;
        window.modal = modal;
        window.filter = filter;
        window.statistics = statistics;
        window.dataProcessor = dataProcessor;
        window.fileHandler = fileHandler;
        window.taskActions = taskActions;
        window.dashboard = this;

        this.init();
    }

    /**
     * 初始化仪表板
     */
    init() {
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeEventListeners();
            this.initializeStateManager();
            this.loadSampleData();
        });
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 这里可以添加其他全局事件监听器
        console.log('仪表板初始化完成');
    }

    /**
     * 初始化状态管理器
     */
    initializeStateManager() {
        if (window.stateManager) {
            window.stateManager.init();

            // 检查是否有可恢复的状态
            if (window.stateManager.hasRestorableState()) {
                console.log('检测到可恢复的数据状态');
                // 如果有可恢复的状态，跳过示例数据加载
                this.skipSampleData = true;
            }
        }
    }

    /**
     * 加载示例数据
     */
    async loadSampleData() {
        // 如果有可恢复的状态，跳过示例数据加载
        if (this.skipSampleData) {
            console.log('跳过示例数据加载，使用恢复的状态');
            return;
        }

        if (window.dataProcessor) {
            await window.dataProcessor.loadSampleData();
        }
    }

    /**
     * 更新仪表板
     */
    updateDashboard() {
        // 更新统计信息
        if (window.statistics) {
            window.statistics.updateStatistics();
        }

        // 渲染任务卡片
        if (window.taskCard && window.filteredTasks) {
            window.taskCard.renderTasks(window.filteredTasks);
        }
    }
}

// 创建仪表板实例
new Dashboard();
