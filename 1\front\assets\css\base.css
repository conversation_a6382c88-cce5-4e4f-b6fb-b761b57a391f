/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 通用按钮样式 */
select, button {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* 状态颜色 */
.completed { color: #27ae60; }
.incomplete { color: #e74c3c; }
.long-term { color: #3498db; }
.time-limited { color: #f39c12; }
.urgent { color: #e74c3c; }

/* 加载状态 */
.loading {
    text-align: center;
    padding: 50px;
    font-size: 1.2em;
    color: #666;
}

/* 文件状态显示 */
.file-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 6px;
    margin-top: 8px;
    font-size: 14px;
    color: #2e7d32;
}

.file-status-icon {
    font-size: 16px;
}

.file-status-text {
    flex: 1;
    font-weight: 500;
}

.file-status-clear {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    padding: 2px 6px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.file-status-clear:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

/* 状态恢复提示 */
.state-restore-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2196f3;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    font-size: 14px;
    font-weight: 500;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.state-restore-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.state-restore-notification.success {
    background: #4caf50;
}

.state-restore-notification.warning {
    background: #ff9800;
}

.state-restore-notification.error {
    background: #f44336;
}

/* 数据加载状态 */
.data-loaded-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: #e8f5e8;
    border-radius: 4px;
    font-size: 12px;
    color: #2e7d32;
    margin-left: 10px;
}

.data-loaded-indicator::before {
    content: "✓";
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
}
