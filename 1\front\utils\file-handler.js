/**
 * 文件处理工具
 */

class FileHandler {
    constructor() {
        this.initEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 文件选择监听
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (event) => {
                this.handleFileSelect(event);
            });
        }

        // 视图切换监听
        const viewSelect = document.getElementById('viewSelect');
        if (viewSelect) {
            viewSelect.addEventListener('change', () => {
                this.updateView();
            });
        }
    }

    /**
     * 处理文件选择
     * @param {Event} event - 文件选择事件
     */
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (window.dataProcessor && window.dataProcessor.isLoading) {
            alert('数据正在加载中，请稍候...');
            return;
        }

        if (window.dataProcessor) {
            window.dataProcessor.isLoading = true;
        }

        console.log('开始处理文件:', file.name);

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                if (file.name.endsWith('.csv')) {
                    window.dataProcessor.parseCSVData(e.target.result);
                } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                    window.dataProcessor.parseExcelData(e.target.result);
                }
                console.log('文件处理完成');

                // 文件处理完成后，检测并启动后端服务
                this.checkAndStartBackendService();
            } catch (error) {
                console.error('文件解析错误:', error);
                alert('文件解析失败，请检查文件格式');
                if (window.dataProcessor) {
                    window.dataProcessor.isLoading = false;
                }
            }
        };

        reader.onerror = () => {
            console.error('文件读取失败');
            alert('文件读取失败');
            if (window.dataProcessor) {
                window.dataProcessor.isLoading = false;
            }
        };

        if (file.name.endsWith('.csv')) {
            reader.readAsText(file, 'utf-8');
        } else {
            reader.readAsArrayBuffer(file);
        }
    }

    /**
     * 加载数据（按钮点击）
     */
    loadData() {
        const fileInput = document.getElementById('fileInput');
        if (!fileInput || fileInput.files.length === 0) {
            alert('请先选择文件');
            return;
        }

        if (window.dataProcessor && window.dataProcessor.isLoading) {
            alert('数据正在加载中，请稍候...');
            return;
        }

        // 创建一个模拟的事件对象
        const mockEvent = {
            target: {
                files: fileInput.files
            }
        };

        this.handleFileSelect(mockEvent);
    }

    /**
     * 导出报告
     */
    exportReport() {
        if (!window.filteredTasks) {
            alert('没有数据可导出');
            return;
        }

        const reportData = {
            生成时间: new Date().toLocaleString('zh-CN'),
            任务总数: window.filteredTasks.length,
            任务列表: window.filteredTasks
        };

        const dataStr = JSON.stringify(reportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `督办报告_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    /**
     * 检测并启动后端服务
     */
    async checkAndStartBackendService() {
        if (window.backendService) {
            await window.backendService.checkAndStart();
        }
    }
}

// 导出文件处理器实例
const fileHandler = new FileHandler();

// 全局函数，供HTML调用
function loadData() {
    fileHandler.loadData();
}

function exportReport() {
    fileHandler.exportReport();
}
