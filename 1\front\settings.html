<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 整改工作台账督办仪表板</title>

    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/header.css">
    <link rel="stylesheet" href="components/settings/settings.css">
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <h1>系统设置</h1>
        <p>配置督办提醒和短信服务参数</p>
        <div class="header-actions">
            <button onclick="window.location.href='index.html'" class="back-btn">
                返回仪表板
            </button>
        </div>
    </div>

    <div class="container">
        <!-- 设置导航 -->
        <div class="settings-nav">
            <div class="nav-item active" data-tab="general">
                <span class="nav-icon">⚙️</span>
                <span>常规设置</span>
            </div>
            <div class="nav-item" data-tab="sms">
                <span class="nav-icon">📱</span>
                <span>短信服务</span>
            </div>
            <div class="nav-item" data-tab="contacts">
                <span class="nav-icon">👥</span>
                <span>联系人管理</span>
            </div>
            <div class="nav-item" data-tab="reminder">
                <span class="nav-icon">⏰</span>
                <span>提醒设置</span>
            </div>
            <div class="nav-item" data-tab="about">
                <span class="nav-icon">ℹ️</span>
                <span>关于</span>
            </div>
        </div>

        <!-- 设置内容区域 -->
        <div class="settings-content">
            <!-- 常规设置 -->
            <div class="settings-panel active" id="general-panel">
                <div class="panel-header">
                    <h2>常规设置</h2>
                    <p>配置系统的基本参数</p>
                </div>

                <div class="settings-group">
                    <h3>任务管理</h3>
                    <div class="setting-item">
                        <label for="urgentThreshold">快到期阈值（天）</label>
                        <input type="number" id="urgentThreshold" min="1" max="60" value="15">
                        <span class="setting-desc">任务距离截止日期多少天内显示为"快到期"</span>
                    </div>
                    <div class="setting-item">
                        <label for="criticalThreshold">紧急阈值（天）</label>
                        <input type="number" id="criticalThreshold" min="1" max="30" value="3">
                        <span class="setting-desc">任务距离截止日期多少天内显示为"紧急"</span>
                    </div>
                    <div class="setting-item">
                        <label for="warningThreshold">警告阈值（天）</label>
                        <input type="number" id="warningThreshold" min="1" max="30" value="7">
                        <span class="setting-desc">任务距离截止日期多少天内显示为"警告"</span>
                    </div>
                    <div class="setting-item">
                        <label for="maxEmptyFields">最大空字段数</label>
                        <input type="number" id="maxEmptyFields" min="0" max="10" value="4">
                        <span class="setting-desc">任务卡片最多允许多少个字段为空（超过则不显示）</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>界面设置</h3>
                    <div class="setting-item">
                        <label for="enableStatisticsFilter">启用统计面板过滤</label>
                        <input type="checkbox" id="enableStatisticsFilter" checked>
                        <span class="setting-desc">点击统计项时是否进行任务过滤</span>
                    </div>
                    <div class="setting-item">
                        <label for="enableAutoSave">自动保存设置</label>
                        <input type="checkbox" id="enableAutoSave">
                        <span class="setting-desc">修改设置时自动保存到本地存储</span>
                    </div>
                </div>
            </div>

            <!-- 短信服务设置 -->
            <div class="settings-panel" id="sms-panel">
                <div class="panel-header">
                    <h2>短信服务设置</h2>
                    <p>配置中电联（CEIC）短信服务参数</p>
                </div>

                <div class="settings-group">
                    <h3>服务状态</h3>
                    <div class="setting-item">
                        <label for="smsEnabled">启用短信提醒</label>
                        <input type="checkbox" id="smsEnabled">
                        <span class="setting-desc">开启后可通过短信发送督办提醒</span>
                    </div>
                    <div class="service-status" id="smsStatus">
                        <span class="status-indicator"></span>
                        <span class="status-text">未配置</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>服务配置</h3>
                    <div class="setting-item">
                        <label for="backendApiUrl">后端API地址</label>
                        <input type="url" id="backendApiUrl" placeholder="http://localhost:5000 (留空使用默认地址)">
                        <span class="setting-desc">后端短信代理服务地址，留空则使用默认配置</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>CEIC 配置</h3>
                    <div class="setting-item">
                        <label for="ceicAccount">平台账号</label>
                        <input type="text" id="ceicAccount" placeholder="请输入CEIC平台账号">
                        <span class="setting-desc">您的CEIC短信平台账号</span>
                    </div>
                    <div class="setting-item">
                        <label for="ceicPassword">平台密码</label>
                        <input type="password" id="ceicPassword" placeholder="请输入CEIC平台密码">
                        <span class="setting-desc">您的CEIC短信平台密码（这里是明文输入，后端会进行MD5加密）</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>发送设置</h3>
                    <div class="setting-item">
                        <label for="batchInterval">批量发送间隔（毫秒）</label>
                        <input type="number" id="batchInterval" min="500" max="5000" value="1000">
                        <span class="setting-desc">批量发送短信时的间隔时间</span>
                    </div>
                    <div class="setting-item">
                        <label for="maxRetries">最大重试次数</label>
                        <input type="number" id="maxRetries" min="1" max="5" value="3">
                        <span class="setting-desc">发送失败时的最大重试次数</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>测试功能</h3>
                    <div class="setting-item">
                        <label for="testPhoneNumber">测试手机号</label>
                        <input type="tel" id="testPhoneNumber" placeholder="请输入测试手机号">
                        <button type="button" id="testSmsBtn" class="test-btn">发送测试短信</button>
                    </div>
                </div>
            </div>

            <!-- 联系人管理 -->
            <div class="settings-panel" id="contacts-panel">
                <div class="panel-header">
                    <h2>联系人管理</h2>
                    <p>配置牵头部门负责人的联系方式</p>
                </div>

                <div class="settings-group">
                    <h3>部门联系人配置</h3>
                    <div class="setting-item">
                        <label>部门联系人映射表</label>
                        <div class="contact-mapping-container">
                            <div class="contact-mapping-header">
                                <span class="contact-name-header">牵头部门</span>
                                <span class="contact-phone-header">负责人手机号</span>
                                <span class="contact-action-header">操作</span>
                            </div>
                            <div id="contactMappingList" class="contact-mapping-list">
                                <!-- 联系人映射项将通过JavaScript动态生成 -->
                            </div>
                            <div class="contact-mapping-actions">
                                <button type="button" id="addContactBtn" class="add-contact-btn">+ 添加部门联系人</button>
                                <button type="button" id="autoDetectContactsBtn" class="auto-detect-btn">自动检测部门</button>
                            </div>
                        </div>
                        <span class="setting-desc">配置牵头部门与负责人手机号的对应关系，用于短信提醒功能</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>批量导入</h3>
                    <div class="setting-item">
                        <label for="contactsTextArea">部门联系人文本</label>
                        <textarea id="contactsTextArea" rows="8" placeholder="请按以下格式输入部门联系人信息，每行一个：
部门名称:负责人手机号
或
部门名称,负责人手机号
或
部门名称 负责人手机号

示例：
党委办公室:13800138001
发展部,13800138002
安全部 13800138003"></textarea>
                        <div class="contact-text-actions">
                            <button type="button" id="parseContactsBtn" class="parse-btn">解析并导入</button>
                            <button type="button" id="clearContactsBtn" class="clear-btn">清空所有</button>
                        </div>
                        <span class="setting-desc">支持多种格式的批量导入，系统会自动解析并验证手机号格式</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>验证设置</h3>
                    <div class="setting-item">
                        <label for="enablePhoneValidation">启用手机号验证</label>
                        <input type="checkbox" id="enablePhoneValidation" checked>
                        <span class="setting-desc">自动验证手机号格式是否正确</span>
                    </div>
                    <div class="setting-item">
                        <label for="autoExtractContacts">自动提取部门</label>
                        <input type="checkbox" id="autoExtractContacts" checked>
                        <span class="setting-desc">从数据文件中自动提取牵头部门名称</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>部门联系人状态</h3>
                    <div class="contact-status" id="contactStatus">
                        <div class="status-item">
                            <span class="status-label">已配置部门：</span>
                            <span class="status-value" id="configuredContactsCount">0</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">有效手机号：</span>
                            <span class="status-value" id="validPhonesCount">0</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">配置完整度：</span>
                            <span class="status-value" id="configCompleteness">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提醒设置 -->
            <div class="settings-panel" id="reminder-panel">
                <div class="panel-header">
                    <h2>提醒设置</h2>
                    <p>配置督办提醒的相关参数</p>
                </div>

                <div class="settings-group">
                    <h3>提醒方式</h3>
                    <div class="setting-item">
                        <label for="enableBrowserNotification">浏览器通知</label>
                        <input type="checkbox" id="enableBrowserNotification" checked>
                        <span class="setting-desc">在浏览器中显示通知提醒</span>
                    </div>
                    <div class="setting-item">
                        <label for="enableSmsNotification">短信通知</label>
                        <input type="checkbox" id="enableSmsNotification">
                        <span class="setting-desc">通过短信发送提醒（需配置短信服务）</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>提醒时机</h3>
                    <div class="setting-item">
                        <label for="reminderDays">提前提醒天数</label>
                        <input type="number" id="reminderDays" min="1" max="30" value="3">
                        <span class="setting-desc">任务到期前多少天开始提醒</span>
                    </div>
                    <div class="setting-item">
                        <label for="dailyReminderTime">每日提醒时间</label>
                        <input type="time" id="dailyReminderTime" value="09:00">
                        <span class="setting-desc">每天定时提醒的时间</span>
                    </div>
                </div>
            </div>

            <!-- 关于页面 -->
            <div class="settings-panel" id="about-panel">
                <div class="panel-header">
                    <h2>关于系统</h2>
                    <p>系统信息和使用说明</p>
                </div>

                <div class="settings-group">
                    <h3>系统信息</h3>
                    <div class="info-item">
                        <span class="info-label">系统名称：</span>
                        <span class="info-value">整改工作台账督办仪表板</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">版本号：</span>
                        <span class="info-value">2.0.0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">更新时间：</span>
                        <span class="info-value">2024-12-19</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>功能说明</h3>
                    <div class="feature-list">
                        <div class="feature-item">
                            <h4>📊 数据管理</h4>
                            <p>支持CSV、Excel文件导入，自动解析和验证数据</p>
                        </div>
                        <div class="feature-item">
                            <h4>📱 短信提醒</h4>
                            <p>集成中电联（CEIC）短信服务，支持单个和批量督办提醒</p>
                        </div>
                        <div class="feature-item">
                            <h4>📈 统计分析</h4>
                            <p>多维度数据统计，支持按部门、类型、状态分类</p>
                        </div>
                        <div class="feature-item">
                            <h4>🔍 智能过滤</h4>
                            <p>支持多种过滤条件，快速定位目标任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="settings-actions">
            <button type="button" id="saveSettingsBtn" class="save-btn">保存设置</button>
            <button type="button" id="resetSettingsBtn" class="reset-btn">重置设置</button>
            <button type="button" id="exportSettingsBtn" class="export-btn">导出配置</button>
            <input type="file" id="importSettingsInput" accept=".json" style="display: none;">
            <button type="button" id="importSettingsBtn" class="import-btn">导入配置</button>
        </div>
    </div>

    <!-- 外部依赖 -->
    <script src="config/app-config.js"></script>
    <script src="utils/config-manager.js"></script>
    <script src="utils/state-manager.js"></script>
    <script src="utils/sms-service.js"></script>
    <script src="components/settings/settings.js"></script>
</body>
</html>
